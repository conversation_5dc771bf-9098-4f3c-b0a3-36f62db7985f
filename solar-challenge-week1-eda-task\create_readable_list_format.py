import pandas as pd

print("🔍 Creating Readable List Format for Unchanged Values...")

# Step 1: Read the matrices
print("📖 Reading matrices...")
original_matrix = pd.read_excel('combined.xlsx', sheet_name='categorycodeandrolecode')
transformed_matrix = pd.read_excel('final_centered_format.xlsx', sheet_name='4_matrix_readable')

# Step 2: Define target columns
target_columns = [
    'Network Admin',
    'Distributor Shop Manager', 
    'Customer Care',
    'Distributor',
    'Sub Super Admin',
    'Agent',
    'Super Admin',
    'POS',
    'Safaricom Shop',
    'Distributor Sales Person',
    'Channel Admin',
    'Sub Distributor'
]

# Step 3: Find column mapping
print("🔍 Mapping columns...")
column_mapping = {}

for trans_col in transformed_matrix.columns:
    if trans_col in target_columns:
        trans_col_idx = list(transformed_matrix.columns).index(trans_col)
        if trans_col_idx < len(original_matrix.columns):
            orig_col = original_matrix.columns[trans_col_idx]
            column_mapping[trans_col] = {
                'original_name': str(orig_col),
                'column_index': trans_col_idx
            }

# Step 4: Extract unchanged values for each column
print("🔍 Extracting unchanged values...")
unchanged_by_column = {}

for trans_col, info in column_mapping.items():
    col_idx = info['column_index']
    
    unchanged_values = []
    
    for row_idx in range(min(len(original_matrix), len(transformed_matrix))):
        orig_val = original_matrix.iloc[row_idx, col_idx]
        trans_val = transformed_matrix.iloc[row_idx, col_idx]
        
        if (pd.notna(orig_val) and pd.notna(trans_val) and 
            str(orig_val).strip() == str(trans_val).strip() and
            str(orig_val).strip() != ''):
            unchanged_values.append(str(orig_val).strip())
    
    # Store unique unchanged values
    unique_unchanged = sorted(list(set(unchanged_values)))
    if unique_unchanged:
        unchanged_by_column[trans_col] = unique_unchanged

# Step 5: Create readable list format
print("📋 Creating readable list format...")
readable_data = []

for trans_col in target_columns:
    if trans_col in unchanged_by_column:
        unchanged_values = unchanged_by_column[trans_col]
        
        # Add column header
        readable_data.append({
            'Column': trans_col,
            'Unchanged_Value': ''
        })
        
        # Add all unchanged values for this column
        for value in unchanged_values:
            readable_data.append({
                'Column': '',
                'Unchanged_Value': value
            })
        
        # Add empty separator row
        readable_data.append({
            'Column': '',
            'Unchanged_Value': ''
        })

# Remove last empty row if exists
if readable_data and readable_data[-1]['Unchanged_Value'] == '':
    readable_data.pop()

# Step 6: Create DataFrame
readable_df = pd.DataFrame(readable_data)

# Step 7: Save to Excel
print("💾 Saving readable list format...")
with pd.ExcelWriter('readable_unchanged_values_list.xlsx', engine='openpyxl') as writer:
    readable_df.to_excel(writer, sheet_name='READABLE_LIST', index=False)

print("🎉 SUCCESS! Created 'readable_unchanged_values_list.xlsx'")

# Step 8: Show preview and create text output
print("\n📊 READABLE LIST FORMAT:")
print(f"Total columns with unchanged values: {len(unchanged_by_column)}")

print("\n📋 UNCHANGED VALUES BY COLUMN:")
print("=" * 50)

for trans_col in target_columns:
    if trans_col in unchanged_by_column:
        unchanged_values = unchanged_by_column[trans_col]
        print(f"\n{trans_col}")
        for value in unchanged_values:
            print(f"    {value}")
        print("-" * 30)

print(f"\n📁 OUTPUT FILE: readable_unchanged_values_list.xlsx")
print("• Single sheet with readable list format")
print("• Column names followed by their unchanged values")
print("• Empty rows separate different columns")

# Step 9: Create summary count
print(f"\n📈 SUMMARY:")
for trans_col in target_columns:
    if trans_col in unchanged_by_column:
        count = len(unchanged_by_column[trans_col])
        print(f"• {trans_col}: {count} unchanged values")
    else:
        print(f"• {trans_col}: 0 unchanged values (clean)")

total_unchanged = sum(len(values) for values in unchanged_by_column.values())
print(f"\n🎯 TOTAL: {total_unchanged} unchanged values across {len(unchanged_by_column)} columns")
