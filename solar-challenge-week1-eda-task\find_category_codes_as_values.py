import pandas as pd

print("🔍 Finding Category Codes Used as Values in Matrix...")

# Step 1: Read the original matrix
print("📖 Reading original matrix...")
original_matrix = pd.read_excel('combined.xlsx', sheet_name='categorycodeandrolecode')

# Also read category lookup for reference
categories_df = pd.read_excel('combined.xlsx', sheet_name='categorycodeandcatagorydescript')
category_dict = dict(zip(categories_df['Category_code'], categories_df['Description']))

print(f"Matrix shape: {original_matrix.shape}")
print(f"Total category codes: {len(category_dict)}")

# Step 2: Get all column headers (these are category codes)
column_headers = [str(col) for col in original_matrix.columns]
print(f"Column headers (category codes): {len(column_headers)}")

# Step 3: Find category codes that appear as values in each column
print("🔍 Analyzing each column for category codes as values...")
results_by_column = {}

for col_idx, col_name in enumerate(original_matrix.columns):
    col_str = str(col_name)
    
    # Get category description
    category_desc = category_dict.get(col_str, col_str)
    
    print(f"Checking column {col_idx + 1}/{len(original_matrix.columns)}: {col_name}")
    
    # Get all unique values in this column
    column_values = original_matrix[col_name].dropna().unique()
    column_values_str = [str(val) for val in column_values]
    
    # Find which of these values are actually category codes (column headers)
    category_codes_found = []
    for value in column_values_str:
        if value in column_headers:
            category_codes_found.append(value)
    
    # Store results if any category codes found
    if category_codes_found:
        results_by_column[col_str] = {
            'column_name': col_str,
            'column_description': category_desc,
            'category_codes_as_values': sorted(category_codes_found),
            'count': len(category_codes_found)
        }

print(f"✓ Found category codes as values in {len(results_by_column)} columns")

# Step 4: Create detailed results
print("📊 Creating detailed analysis...")
detailed_data = []

for col_code, info in results_by_column.items():
    for cat_code in info['category_codes_as_values']:
        # Get description of the category code that appears as value
        cat_desc = category_dict.get(cat_code, cat_code)
        
        detailed_data.append({
            'Column_Code': info['column_name'],
            'Column_Description': info['column_description'],
            'Category_Code_as_Value': cat_code,
            'Category_Description': cat_desc,
            'Issue': f"Category code '{cat_code}' appears as value in column '{col_code}'"
        })

detailed_df = pd.DataFrame(detailed_data)

# Step 5: Create summary by column
print("📈 Creating summary...")
summary_data = []
for col_code, info in results_by_column.items():
    summary_data.append({
        'Column_Code': info['column_name'],
        'Column_Description': info['column_description'],
        'Count_Category_Codes': info['count'],
        'Category_Codes_Found': ', '.join(info['category_codes_as_values'])
    })

summary_df = pd.DataFrame(summary_data)

# Step 6: Create individual column sheets
print("📋 Creating individual column analysis...")
column_sheets = {}

for col_code, info in results_by_column.items():
    col_data = []
    for cat_code in info['category_codes_as_values']:
        cat_desc = category_dict.get(cat_code, cat_code)
        col_data.append({
            'Category_Code': cat_code,
            'Category_Description': cat_desc,
            'Issue_Type': 'Category code used as role value'
        })
    column_sheets[col_code] = pd.DataFrame(col_data)

# Step 7: Save to Excel
print("💾 Saving category codes analysis...")
with pd.ExcelWriter('category_codes_as_values.xlsx', engine='openpyxl') as writer:
    # Summary sheet
    summary_df.to_excel(writer, sheet_name='1_SUMMARY', index=False)
    
    # Detailed list
    detailed_df.to_excel(writer, sheet_name='2_DETAILED_LIST', index=False)
    
    # Individual column sheets
    sheet_count = 0
    for col_code, col_df in column_sheets.items():
        if sheet_count < 20:  # Limit sheets
            safe_sheet_name = str(col_code)[:25]
            col_df.to_excel(writer, sheet_name=f'3_{safe_sheet_name}', index=False)
            sheet_count += 1

print("🎉 SUCCESS! Created 'category_codes_as_values.xlsx'")

# Step 8: Show preview
print("\n📊 CATEGORY CODES AS VALUES PREVIEW:")
print(f"Columns with category codes as values: {len(results_by_column)}")
print(f"Total instances found: {len(detailed_df)}")

print("\n📈 COLUMNS WITH MOST CATEGORY CODES AS VALUES:")
if not summary_df.empty:
    top_columns = summary_df.nlargest(10, 'Count_Category_Codes')
    print(top_columns.to_string(index=False))

print("\n📋 EXAMPLE - Network Admin column contains:")
nwadm_info = results_by_column.get('NWADM', None)
if nwadm_info:
    print(f"Category codes found: {', '.join(nwadm_info['category_codes_as_values'])}")
else:
    print("No category codes found in NWADM column")

print(f"\n📁 OUTPUT FILE: category_codes_as_values.xlsx")
print("• Sheet 1: Summary by column")
print("• Sheet 2: Detailed list of all instances")
print("• Sheet 3+: Individual analysis for each column")

print(f"\n🎯 INTERPRETATION:")
print("These are category codes that appear as values in other columns,")
print("indicating potential data structure issues or cross-references.")
