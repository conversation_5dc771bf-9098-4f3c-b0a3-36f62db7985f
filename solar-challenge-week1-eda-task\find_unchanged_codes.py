import pandas as pd

print("🔍 Finding Unchanged Role Codes...")

# Step 1: Read the original and transformed matrices
print("📖 Reading matrices...")
original_matrix = pd.read_excel('combined.xlsx', sheet_name='categorycodeandrolecode')
transformed_matrix = pd.read_excel('final_centered_format.xlsx', sheet_name='4_matrix_readable')

# Also read the lookup tables
roles_df = pd.read_excel('combined.xlsx', sheet_name='role_codeandrolecdescription')
categories_df = pd.read_excel('combined.xlsx', sheet_name='categorycodeandcatagorydescript')

# Create lookup dictionaries
role_dict = dict(zip(roles_df['Role_ Code'], roles_df['Role_Name']))
category_dict = dict(zip(categories_df['Category_code'], categories_df['Description']))

print(f"Original matrix shape: {original_matrix.shape}")
print(f"Transformed matrix shape: {transformed_matrix.shape}")

# Step 2: Find unchanged values
print("🔍 Comparing matrices to find unchanged role codes...")
unchanged_data = []

for col_idx, orig_col in enumerate(original_matrix.columns):
    # Get corresponding transformed column
    if col_idx < len(transformed_matrix.columns):
        trans_col = transformed_matrix.columns[col_idx]
        
        print(f"Checking column {col_idx + 1}/{len(original_matrix.columns)}: {orig_col}")
        
        # Get category description
        category_desc = category_dict.get(str(orig_col), str(orig_col))
        
        # Compare values in this column
        orig_values = original_matrix[orig_col].dropna().unique()
        trans_values = transformed_matrix[trans_col].dropna().unique()
        
        # Find values that didn't change (exist in both original and transformed)
        unchanged_in_column = []
        
        for orig_val in orig_values:
            orig_str = str(orig_val)
            # Check if this value appears unchanged in transformed data
            if orig_str in [str(tv) for tv in trans_values]:
                # This means it wasn't transformed (role code not found in lookup)
                unchanged_in_column.append(orig_str)
        
        # Add to results
        if unchanged_in_column:
            for unchanged_code in unchanged_in_column:
                unchanged_data.append({
                    'Category_Code': str(orig_col),
                    'Category_Description': category_desc,
                    'Unchanged_Role_Code': unchanged_code,
                    'Status': 'Not found in role lookup table'
                })

# Step 3: Also find unmapped category codes
print("🔍 Finding unmapped category codes...")
unmapped_categories = []

for col in original_matrix.columns:
    col_str = str(col)
    if col_str not in category_dict:
        unmapped_categories.append({
            'Category_Code': col_str,
            'Category_Description': 'UNMAPPED - No description found',
            'Unchanged_Role_Code': 'N/A - Category itself unmapped',
            'Status': 'Category code not found in category lookup table'
        })

# Combine all unchanged data
all_unchanged = unchanged_data + unmapped_categories

# Step 4: Create DataFrame
unchanged_df = pd.DataFrame(all_unchanged)

print(f"✓ Found {len(unchanged_data)} unchanged role codes")
print(f"✓ Found {len(unmapped_categories)} unmapped category codes")
print(f"✓ Total issues: {len(all_unchanged)}")

# Step 5: Group by category for better organization
print("📊 Organizing by category...")
if not unchanged_df.empty:
    # Sort by category
    unchanged_df = unchanged_df.sort_values(['Category_Code', 'Unchanged_Role_Code'])
    
    # Create summary by category
    category_summary = unchanged_df.groupby('Category_Code').agg({
        'Unchanged_Role_Code': 'count',
        'Category_Description': 'first'
    }).rename(columns={'Unchanged_Role_Code': 'Count_of_Issues'}).reset_index()

# Step 6: Save results
print("💾 Saving unchanged codes analysis...")
with pd.ExcelWriter('unchanged_codes_analysis.xlsx', engine='openpyxl') as writer:
    # Main unchanged codes list
    unchanged_df.to_excel(writer, sheet_name='1_UNCHANGED_CODES', index=False)
    
    # Summary by category
    if not unchanged_df.empty:
        category_summary.to_excel(writer, sheet_name='2_SUMMARY_BY_CATEGORY', index=False)
    
    # Original data for reference
    original_matrix.to_excel(writer, sheet_name='3_original_matrix', index=False)
    transformed_matrix.to_excel(writer, sheet_name='4_transformed_matrix', index=False)

print("🎉 SUCCESS! Created 'unchanged_codes_analysis.xlsx'")

# Step 7: Show preview
print("\n📊 UNCHANGED CODES PREVIEW:")
if not unchanged_df.empty:
    print(f"Total unchanged items: {len(unchanged_df)}")
    print("\nFirst 10 unchanged codes:")
    print(unchanged_df.head(10).to_string(index=False))
    
    print(f"\n📈 SUMMARY BY CATEGORY:")
    if not category_summary.empty:
        print(category_summary.to_string(index=False))
else:
    print("No unchanged codes found - all transformations were successful!")

print(f"\n📁 OUTPUT FILE: unchanged_codes_analysis.xlsx")
print("• Sheet 1: Detailed list of unchanged codes")
print("• Sheet 2: Summary by category") 
print("• Sheet 3: Original matrix (reference)")
print("• Sheet 4: Transformed matrix (reference)")
