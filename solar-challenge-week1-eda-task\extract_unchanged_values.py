import pandas as pd

print("🔍 Extracting Unchanged Values from Matrix Comparison...")

# Step 1: Read the original and transformed matrices
print("📖 Reading matrices...")
original_matrix = pd.read_excel('combined.xlsx', sheet_name='categorycodeandrolecode')
transformed_matrix = pd.read_excel('final_centered_format.xlsx', sheet_name='4_matrix_readable')

# Also read category lookup for column names
categories_df = pd.read_excel('combined.xlsx', sheet_name='categorycodeandcatagorydescript')
category_dict = dict(zip(categories_df['Category_code'], categories_df['Description']))

print(f"Original matrix shape: {original_matrix.shape}")
print(f"Transformed matrix shape: {transformed_matrix.shape}")

# Step 2: Compare matrices cell by cell
print("🔍 Comparing matrices cell by cell...")
unchanged_by_column = {}

for col_idx, orig_col in enumerate(original_matrix.columns):
    if col_idx < len(transformed_matrix.columns):
        trans_col = transformed_matrix.columns[col_idx]
        
        print(f"Processing column {col_idx + 1}/{len(original_matrix.columns)}: {orig_col}")
        
        # Get category description
        category_desc = category_dict.get(str(orig_col), str(orig_col))
        
        # Find unchanged values in this column
        unchanged_values = []
        
        for row_idx in range(len(original_matrix)):
            if row_idx < len(transformed_matrix):
                orig_val = original_matrix.iloc[row_idx, col_idx]
                trans_val = transformed_matrix.iloc[row_idx, col_idx]
                
                # Check if both values exist and are the same
                if (pd.notna(orig_val) and pd.notna(trans_val) and 
                    str(orig_val) == str(trans_val)):
                    unchanged_values.append(str(orig_val))
        
        # Remove duplicates and store
        unique_unchanged = list(set(unchanged_values))
        if unique_unchanged:
            unchanged_by_column[orig_col] = {
                'category_code': str(orig_col),
                'category_description': category_desc,
                'unchanged_values': unique_unchanged,
                'count': len(unique_unchanged)
            }

print(f"✓ Found unchanged values in {len(unchanged_by_column)} columns")

# Step 3: Create detailed list for Excel
print("📊 Creating detailed unchanged values list...")
detailed_data = []

for col_code, info in unchanged_by_column.items():
    for value in info['unchanged_values']:
        detailed_data.append({
            'Category_Code': info['category_code'],
            'Category_Description': info['category_description'],
            'Unchanged_Value': value
        })

detailed_df = pd.DataFrame(detailed_data)

# Step 4: Create summary by column
print("📈 Creating summary by column...")
summary_data = []
for col_code, info in unchanged_by_column.items():
    summary_data.append({
        'Category_Code': info['category_code'],
        'Category_Description': info['category_description'],
        'Count_Unchanged_Values': info['count'],
        'Sample_Values': ', '.join(info['unchanged_values'][:5]) + ('...' if len(info['unchanged_values']) > 5 else '')
    })

summary_df = pd.DataFrame(summary_data)

# Step 5: Create separate sheets for each problematic column
print("📋 Creating individual column sheets...")
column_sheets = {}

for col_code, info in unchanged_by_column.items():
    if info['count'] > 0:
        col_data = []
        for value in info['unchanged_values']:
            col_data.append({
                'Unchanged_Role_Code': value,
                'Status': 'Not transformed - likely missing from role lookup'
            })
        column_sheets[col_code] = pd.DataFrame(col_data)

# Step 6: Save to Excel
print("💾 Saving unchanged values analysis...")
with pd.ExcelWriter('unchanged_values_by_column.xlsx', engine='openpyxl') as writer:
    # Summary sheet
    summary_df.to_excel(writer, sheet_name='1_SUMMARY', index=False)
    
    # Detailed list
    detailed_df.to_excel(writer, sheet_name='2_DETAILED_LIST', index=False)
    
    # Individual column sheets (limit to first 20 to avoid too many sheets)
    sheet_count = 0
    for col_code, col_df in column_sheets.items():
        if sheet_count < 20:  # Excel has sheet limit
            safe_sheet_name = str(col_code)[:25]  # Limit sheet name length
            col_df.to_excel(writer, sheet_name=f'3_{safe_sheet_name}', index=False)
            sheet_count += 1

print("🎉 SUCCESS! Created 'unchanged_values_by_column.xlsx'")

# Step 7: Show preview
print("\n📊 UNCHANGED VALUES PREVIEW:")
print(f"Total columns with unchanged values: {len(unchanged_by_column)}")
print(f"Total unchanged values found: {len(detailed_df)}")

print("\n📈 TOP 10 COLUMNS WITH MOST UNCHANGED VALUES:")
if not summary_df.empty:
    top_columns = summary_df.nlargest(10, 'Count_Unchanged_Values')
    print(top_columns[['Category_Code', 'Category_Description', 'Count_Unchanged_Values']].to_string(index=False))

print("\n📋 SAMPLE UNCHANGED VALUES:")
if not detailed_df.empty:
    print(detailed_df.head(10).to_string(index=False))

print(f"\n📁 OUTPUT FILE: unchanged_values_by_column.xlsx")
print("• Sheet 1: Summary by column")
print("• Sheet 2: Detailed list of all unchanged values")
print("• Sheet 3+: Individual sheets for each problematic column")

print(f"\n🎯 NEXT STEPS:")
print("1. Review the unchanged values in each column")
print("2. Add missing role codes to your role lookup table")
print("3. Re-run the transformation after updating lookups")
