# Import libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats

# 1. Load data
df = pd.read_csv("../data/raw/benin.csv", parse_dates=['Timestamp'])
print(f"Data shape: {df.shape}")
print(df.head())

# 2. Initial profiling
print("\n=== Data Types ===")
print(df.dtypes)

print("\n=== Summary Stats ===")
print(df.describe(include='all', datetime_is_numeric=True))

print("\n=== Missing Values ===")
print(df.isnull().sum().sort_values(ascending=False))

# 3. Handle missing data
# Forward fill for time-series
df.fillna(method='ffill', inplace=True)

# 4. Outlier detection (Z-scores > 3)
numeric_cols = df.select_dtypes(include=np.number).columns
z_scores = stats.zscore(df[numeric_cols])
outliers = (np.abs(z_scores) > 3).any(axis=1)
print(f"\nFound {outliers.sum()} outliers ({(outliers.sum()/len(df))*100:.2f}%)")

# Optional: Remove outliers
# df = df[~outliers]

# 5. Time-series visualization
plt.figure(figsize=(12, 6))
df.set_index('Timestamp')['GHI'].plot(title='GHI Time Series (Benin)')
plt.ylabel('GHI (W/m²)')
plt.tight_layout()
plt.savefig('../figures/benin_ghi_timeseries.png')
plt.show()

# 6. Correlation analysis
corr_matrix = df[numeric_cols].corr()
plt.figure(figsize=(12, 8))
sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', center=0)
plt.title('Correlation Matrix (Benin)')
plt.tight_layout()
plt.savefig('../figures/benin_correlation.png')
plt.show()

# 7. Export cleaned data
df.to_csv('../data/processed/benin_clean.csv', index=False)