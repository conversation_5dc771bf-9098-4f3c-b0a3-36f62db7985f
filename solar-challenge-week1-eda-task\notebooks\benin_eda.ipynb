{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from scipy import stats\n", "\n", "# 1. Load data\n", "df = pd.read_csv(\"../data/raw/benin.csv\", parse_dates=['Timestamp'])\n", "print(f\"Data shape: {df.shape}\")\n", "print(df.head())\n", "\n", "# 2. Initial profiling\n", "print(\"\\n=== Data Types ===\")\n", "print(df.dtypes)\n", "\n", "print(\"\\n=== Summary Stats ===\")\n", "print(df.describe(include='all', datetime_is_numeric=True))\n", "\n", "print(\"\\n=== Missing Values ===\")\n", "print(df.isnull().sum().sort_values(ascending=False))\n", "\n", "# 3. <PERSON><PERSON> missing data\n", "# Forward fill for time-series\n", "df.fillna(method='ffill', inplace=True)\n", "\n", "# 4. Outlier detection (Z-scores > 3)\n", "numeric_cols = df.select_dtypes(include=np.number).columns\n", "z_scores = stats.zscore(df[numeric_cols])\n", "outliers = (np.abs(z_scores) > 3).any(axis=1)\n", "print(f\"\\nFound {outliers.sum()} outliers ({(outliers.sum()/len(df))*100:.2f}%)\")\n", "\n", "# Optional: Remove outliers\n", "# df = df[~outliers]\n", "\n", "# 5. Time-series visualization\n", "plt.figure(figsize=(12, 6))\n", "df.set_index('Timestamp')['GHI'].plot(title='GHI Time Series (Benin)')\n", "plt.ylabel('GHI (W/m²)')\n", "plt.tight_layout()\n", "plt.savefig('../figures/benin_ghi_timeseries.png')\n", "plt.show()\n", "\n", "# 6. Correlation analysis\n", "corr_matrix = df[numeric_cols].corr()\n", "plt.figure(figsize=(12, 8))\n", "sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', center=0)\n", "plt.title('Correlation Matrix (Benin)')\n", "plt.tight_layout()\n", "plt.savefig('../figures/benin_correlation.png')\n", "plt.show()\n", "\n", "# 7. Export cleaned data\n", "df.to_csv('../data/processed/benin_clean.csv', index=False)"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}