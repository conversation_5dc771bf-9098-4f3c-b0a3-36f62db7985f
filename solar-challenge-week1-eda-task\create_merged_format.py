import pandas as pd

print("🔄 Creating Merged Category-Role Format...")

# Step 1: Read all sheets
print("📖 Reading Excel sheets...")
roles_df = pd.read_excel('combined.xlsx', sheet_name='role_codeandrolecdescription')
categories_df = pd.read_excel('combined.xlsx', sheet_name='categorycodeandcatagorydescript') 
matrix_df = pd.read_excel('combined.xlsx', sheet_name='categorycodeandrolecode')

# Step 2: Create lookup dictionaries
print("🔍 Creating lookup dictionaries...")
role_dict = dict(zip(roles_df['Role_ Code'], roles_df['Role_Name']))
category_dict = dict(zip(categories_df['Category_code'], categories_df['Description']))

# Step 3: Create merged format
print("📋 Creating merged category-role format...")
merged_data = []

for col_idx, col in enumerate(matrix_df.columns):
    col_str = str(col)
    
    # Get category description
    if col_str in category_dict:
        category_desc = category_dict[col_str]
    else:
        category_desc = col_str  # Keep original if no match
    
    print(f"Processing {col_idx + 1}/{len(matrix_df.columns)}: {category_desc}")
    
    # Get all unique roles in this category
    roles_in_category = matrix_df[col].dropna().unique()
    
    if len(roles_in_category) > 0:
        # Sort roles
        sorted_roles = sorted(roles_in_category, key=str)
        
        for role_idx, role_code in enumerate(sorted_roles):
            role_str = str(role_code)
            
            # Get role description
            if role_str in role_dict:
                role_desc = role_dict[role_str]
            else:
                role_desc = role_str  # Keep original if no match
            
            # Add row - category name only for first role, empty for others
            if role_idx == 0:
                # First role gets the category name
                merged_data.append({
                    'Category': category_desc,
                    'Role': role_desc
                })
            else:
                # Subsequent roles get empty category (for merging)
                merged_data.append({
                    'Category': '',
                    'Role': role_desc
                })

# Step 4: Create DataFrame
merged_df = pd.DataFrame(merged_data)

print(f"✓ Created merged format with {len(merged_df)} rows")

# Step 5: Create the other sheets as before
print("🔄 Creating other sheets...")

# Flattened format
flattened_data = []
for col in matrix_df.columns:
    col_str = str(col)
    category_desc = category_dict.get(col_str, col_str)
    
    roles_in_category = matrix_df[col].dropna().unique()
    for role_code in roles_in_category:
        role_str = str(role_code)
        role_desc = role_dict.get(role_str, role_str)
        flattened_data.append({
            'Category': category_desc,
            'Role': role_desc
        })

flattened_df = pd.DataFrame(flattened_data).drop_duplicates().sort_values(['Category', 'Role']).reset_index(drop=True)

# Transformed matrix
transformed_df = matrix_df.copy()
new_columns = [category_dict.get(str(col), str(col)) for col in matrix_df.columns]
transformed_df.columns = new_columns

for col in transformed_df.columns:
    for idx in transformed_df.index:
        cell_value = transformed_df.at[idx, col]
        if pd.notna(cell_value):
            cell_str = str(cell_value)
            if cell_str in role_dict:
                transformed_df.at[idx, col] = role_dict[cell_str]

# Step 6: Save to Excel
print("💾 Saving to Excel file...")
with pd.ExcelWriter('final_merged_format.xlsx', engine='openpyxl') as writer:
    # Original sheets
    roles_df.to_excel(writer, sheet_name='1_roles_original', index=False)
    categories_df.to_excel(writer, sheet_name='2_categories_original', index=False)
    matrix_df.to_excel(writer, sheet_name='3_matrix_original', index=False)
    
    # Transformed matrix
    transformed_df.to_excel(writer, sheet_name='4_matrix_readable', index=False)
    
    # Flattened list
    flattened_df.to_excel(writer, sheet_name='5_flattened_list', index=False)
    
    # NEW: Merged format - category spans multiple role rows
    merged_df.to_excel(writer, sheet_name='6_MERGED_FORMAT', index=False)

print("🎉 SUCCESS! Created 'final_merged_format.xlsx'")

# Step 7: Show preview
print("\n📊 MERGED FORMAT PREVIEW:")
print(f"Total rows: {len(merged_df)}")
print("\nFirst 15 rows showing the merged format:")

current_category = None
for i, row in merged_df.head(15).iterrows():
    if row['Category']:  # New category
        current_category = row['Category']
        print(f"{current_category:<25} | {row['Role']}")
    else:  # Role under same category
        print(f"{'':25} | {row['Role']}")

print(f"\n📁 FILE STRUCTURE:")
print("• Sheet 1: Original roles")
print("• Sheet 2: Original categories")  
print("• Sheet 3: Original matrix")
print("• Sheet 4: Readable matrix")
print("• Sheet 5: Flattened list (Category | Role)")
print("• Sheet 6: MERGED FORMAT (Category spans multiple role rows)")

print(f"\n✅ File ready: final_merged_format.xlsx")
print("📝 Note: In Excel, you can merge cells in column A for each category group to get the visual effect you want.")
