# Blackbox Exporter Configuration for Endpoint Monitoring
modules:
  # HTTP 2xx probe
  http_2xx:
    prober: http
    timeout: 5s
    http:
      valid_http_versions: ["HTTP/1.1", "HTTP/2.0"]
      valid_status_codes: [200, 201, 202, 204]
      method: GET
      headers:
        Host: vhost.example.com
        Accept-Language: en-US
      no_follow_redirects: false
      fail_if_ssl: false
      fail_if_not_ssl: false
      tls_config:
        insecure_skip_verify: false
      preferred_ip_protocol: "ip4"

  # HTTP POST probe
  http_post_2xx:
    prober: http
    timeout: 5s
    http:
      method: POST
      headers:
        Content-Type: application/json
      body: '{"health": "check"}'
      valid_status_codes: [200, 201, 202]

  # TCP connect probe
  tcp_connect:
    prober: tcp
    timeout: 5s
    tcp:
      preferred_ip_protocol: "ip4"

  # ICMP ping probe
  icmp:
    prober: icmp
    timeout: 5s
    icmp:
      preferred_ip_protocol: "ip4"

  # DNS probe
  dns:
    prober: dns
    timeout: 5s
    dns:
      query_name: "example.com"
      query_type: "A"
      valid_rcodes:
      - NOERROR
      validate_answer_rrs:
        fail_if_matches_regexp:
        - ".*127.0.0.1"
        fail_if_not_matches_regexp:
        - "www.prometheus.io.\t300\tIN\tA\t46.101.158.64"

  # HTTP probe with basic auth
  http_2xx_basic_auth:
    prober: http
    timeout: 5s
    http:
      method: GET
      valid_status_codes: [200]
      basic_auth:
        username: "monitoring"
        password: "secret"

  # HTTPS probe
  https_2xx:
    prober: http
    timeout: 5s
    http:
      method: GET
      valid_status_codes: [200]
      tls_config:
        insecure_skip_verify: true

  # Custom probe for your Java applications
  java_app_health:
    prober: http
    timeout: 10s
    http:
      method: GET
      valid_status_codes: [200]
      headers:
        Accept: application/json
      body_size_limit: 1024
      fail_if_body_matches_regexp:
      - "error"
      - "failed"
      fail_if_body_not_matches_regexp:
      - "status.*ok"

  # SMS Gateway health check
  sms_gateway_health:
    prober: http
    timeout: 5s
    http:
      method: GET
      valid_status_codes: [200, 204]
      headers:
        User-Agent: "Prometheus-Blackbox-Exporter"

  # Bearer service health check
  bearer_service_health:
    prober: http
    timeout: 5s
    http:
      method: GET
      valid_status_codes: [200]
      headers:
        Accept: application/json
