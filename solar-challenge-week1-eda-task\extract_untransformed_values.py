import pandas as pd

print("🔍 Extracting Untransformed Values from Readable Matrix...")

# Step 1: Read the transformed matrix and lookup tables
print("📖 Reading data...")
transformed_matrix = pd.read_excel('final_centered_format.xlsx', sheet_name='4_matrix_readable')
roles_df = pd.read_excel('combined.xlsx', sheet_name='role_codeandrolecdescription')
categories_df = pd.read_excel('combined.xlsx', sheet_name='categorycodeandcatagorydescript')

# Create lookup sets for checking
role_names = set(roles_df['Role_Name'].astype(str))
role_codes = set(roles_df['Role_ Code'].astype(str))
category_descriptions = set(categories_df['Description'].astype(str))

print(f"Transformed matrix shape: {transformed_matrix.shape}")
print(f"Known role names: {len(role_names)}")
print(f"Known role codes: {len(role_codes)}")

# Step 2: Analyze each column for untransformed values
print("🔍 Finding untransformed values in each column...")
untransformed_by_column = {}

for col_idx, col_name in enumerate(transformed_matrix.columns):
    print(f"Analyzing column {col_idx + 1}/{len(transformed_matrix.columns)}: {col_name}")
    
    # Get all unique non-null values in this column
    column_values = transformed_matrix[col_name].dropna().unique()
    
    # Find values that are likely untransformed (still codes, not descriptions)
    untransformed_values = []
    
    for value in column_values:
        value_str = str(value).strip()
        
        # Skip empty values
        if not value_str or value_str == 'nan':
            continue
            
        # Check if this value looks like an untransformed code
        # (exists in role_codes but not in role_names, or looks like a code pattern)
        is_untransformed = False
        
        # Method 1: Value exists in role codes (meaning it wasn't transformed)
        if value_str in role_codes:
            is_untransformed = True
        
        # Method 2: Value looks like a code pattern (uppercase, short, alphanumeric)
        elif (len(value_str) <= 15 and 
              value_str.isupper() and 
              any(c.isdigit() for c in value_str) and
              value_str not in role_names and
              value_str not in category_descriptions):
            is_untransformed = True
        
        # Method 3: Value contains typical code patterns
        elif (any(pattern in value_str for pattern in ['AUT', 'R_', 'USR', 'ADM']) and
              value_str not in role_names):
            is_untransformed = True
        
        if is_untransformed:
            untransformed_values.append(value_str)
    
    # Store results if untransformed values found
    if untransformed_values:
        untransformed_by_column[col_name] = {
            'column_name': col_name,
            'untransformed_values': sorted(set(untransformed_values)),
            'count': len(set(untransformed_values))
        }

print(f"✓ Found untransformed values in {len(untransformed_by_column)} columns")

# Step 3: Create detailed results
print("📊 Creating detailed analysis...")
detailed_data = []

for col_name, info in untransformed_by_column.items():
    for value in info['untransformed_values']:
        detailed_data.append({
            'Column_Name': col_name,
            'Untransformed_Value': value,
            'Issue': 'Value not transformed to readable description'
        })

detailed_df = pd.DataFrame(detailed_data)

# Step 4: Create summary by column
summary_data = []
for col_name, info in untransformed_by_column.items():
    summary_data.append({
        'Column_Name': col_name,
        'Count_Untransformed': info['count'],
        'Sample_Values': ', '.join(info['untransformed_values'][:5]) + ('...' if len(info['untransformed_values']) > 5 else '')
    })

summary_df = pd.DataFrame(summary_data)

# Step 5: Create individual column sheets
print("📋 Creating individual column sheets...")
column_sheets = {}

for col_name, info in untransformed_by_column.items():
    col_data = []
    for value in info['untransformed_values']:
        col_data.append({
            'Untransformed_Value': value,
            'Likely_Issue': 'Missing from role lookup table'
        })
    column_sheets[col_name] = pd.DataFrame(col_data)

# Step 6: Save to Excel
print("💾 Saving untransformed values analysis...")
with pd.ExcelWriter('untransformed_values_by_column.xlsx', engine='openpyxl') as writer:
    # Summary sheet
    summary_df.to_excel(writer, sheet_name='1_SUMMARY', index=False)
    
    # Detailed list
    detailed_df.to_excel(writer, sheet_name='2_ALL_UNTRANSFORMED', index=False)
    
    # Individual column sheets
    sheet_count = 0
    for col_name, col_df in column_sheets.items():
        if sheet_count < 20:  # Limit sheets
            safe_sheet_name = str(col_name)[:25].replace('/', '_')
            col_df.to_excel(writer, sheet_name=f'3_{safe_sheet_name}', index=False)
            sheet_count += 1

print("🎉 SUCCESS! Created 'untransformed_values_by_column.xlsx'")

# Step 7: Show preview
print("\n📊 UNTRANSFORMED VALUES PREVIEW:")
print(f"Columns with untransformed values: {len(untransformed_by_column)}")
print(f"Total untransformed values: {len(detailed_df)}")

print("\n📈 COLUMNS WITH MOST UNTRANSFORMED VALUES:")
if not summary_df.empty:
    top_columns = summary_df.nlargest(10, 'Count_Untransformed')
    print(top_columns.to_string(index=False))

print("\n📋 SAMPLE UNTRANSFORMED VALUES:")
if not detailed_df.empty:
    print(detailed_df.head(15).to_string(index=False))

print(f"\n📁 OUTPUT FILE: untransformed_values_by_column.xlsx")
print("• Sheet 1: Summary by column")
print("• Sheet 2: All untransformed values")
print("• Sheet 3+: Individual sheets for each column")

print(f"\n🎯 NEXT STEPS:")
print("1. Review untransformed values in each column")
print("2. Add missing role definitions to lookup table")
print("3. Re-run transformation for complete results")
