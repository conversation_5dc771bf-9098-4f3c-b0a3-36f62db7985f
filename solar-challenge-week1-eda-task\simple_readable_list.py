import pandas as pd

# Read data
original = pd.read_excel('combined.xlsx', sheet_name='categorycodeandrolecode')
transformed = pd.read_excel('final_centered_format.xlsx', sheet_name='4_matrix_readable')

# Target columns
targets = ['Network Admin', 'Distributor Shop Manager', 'Customer Care', 'Distributor', 
          'Sub Super Admin', 'Agent', 'Super Admin', 'POS', 'Safaricom Shop', 
          'Distributor Sales Person', 'Channel Admin', 'Sub Distributor']

# Find unchanged values
results = []

for col in transformed.columns:
    if col in targets:
        col_idx = list(transformed.columns).index(col)
        if col_idx < len(original.columns):
            # Compare values
            unchanged = []
            for i in range(len(original)):
                if i < len(transformed):
                    orig_val = original.iloc[i, col_idx]
                    trans_val = transformed.iloc[i, col_idx]
                    if (pd.notna(orig_val) and pd.notna(trans_val) and 
                        str(orig_val) == str(trans_val) and str(orig_val).strip()):
                        unchanged.append(str(orig_val))
            
            # Add to results
            if unchanged:
                unique_unchanged = sorted(list(set(unchanged)))
                results.append({'Column': col, 'Unchanged_Value': ''})
                for val in unique_unchanged:
                    results.append({'Column': '', 'Unchanged_Value': val})
                results.append({'Column': '', 'Unchanged_Value': ''})

# Create DataFrame and save
df = pd.DataFrame(results)
df.to_excel('readable_list.xlsx', index=False)

print("✅ Created readable_list.xlsx")
print(f"Total rows: {len(df)}")

# Show preview
print("\nPreview:")
for i, row in df.head(30).iterrows():
    if row['Column']:
        print(f"\n{row['Column']}")
    elif row['Unchanged_Value']:
        print(f"    {row['Unchanged_Value']}")
