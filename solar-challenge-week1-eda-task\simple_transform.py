import pandas as pd

print("Step 1: Reading sheets...")

# Read the sheets one by one
roles_df = pd.read_excel('combined.xlsx', sheet_name='role_codeandrolecdescription')
print(f"Roles sheet: {roles_df.shape}")

categories_df = pd.read_excel('combined.xlsx', sheet_name='categorycodeandcatagorydescript') 
print(f"Categories sheet: {categories_df.shape}")

matrix_df = pd.read_excel('combined.xlsx', sheet_name='categorycodeandrolecode')
print(f"Matrix sheet: {matrix_df.shape}")

print("Step 2: Creating lookup dictionaries...")

# Create dictionaries for mapping
role_dict = dict(zip(roles_df['Role_ Code'], roles_df['Role_Name']))
category_dict = dict(zip(categories_df['Category_code'], categories_df['Description']))

print(f"Role dictionary: {len(role_dict)} entries")
print(f"Category dictionary: {len(category_dict)} entries")

print("Step 3: Sample transformations...")
print("Sample role mappings:")
for i, (code, name) in enumerate(list(role_dict.items())[:3]):
    print(f"  {code} -> {name}")

print("Sample category mappings:")
for i, (code, desc) in enumerate(list(category_dict.items())[:3]):
    print(f"  {code} -> {desc}")

print("Done with basic setup!")
