# Prometheus Values for Corporate Environment
prometheus:
  prometheusSpec:
    # Storage configuration
    storageSpec:
      volumeClaimTemplate:
        spec:
          storageClassName: "your-storage-class"
          accessModes: ["ReadWriteOnce"]
          resources:
            requests:
              storage: 100Gi
    
    # Retention policy
    retention: 30d
    retentionSize: 90GB
    
    # Resource limits
    resources:
      requests:
        memory: 2Gi
        cpu: 1000m
      limits:
        memory: 4Gi
        cpu: 2000m
    
    # Service monitor selector
    serviceMonitorSelectorNilUsesHelmValues: false
    podMonitorSelectorNilUsesHelmValues: false
    ruleSelectorNilUsesHelmValues: false
    
    # External URL for corporate access
    externalUrl: "http://prometheus.yourcompany.local:9090"
    
    # Additional scrape configs for your services
    additionalScrapeConfigs:
      - job_name: 'alloy-metrics'
        static_configs:
          - targets: ['alloy-service:12345']
      
      - job_name: 'java-applications'
        static_configs:
          - targets: 
            - 'java-app-1:8007'
            - 'java-app-2:9899'
            - 'java-app-3:5555'
      
      - job_name: 'sms-gateway'
        static_configs:
          - targets: ['sms-gateway:7788']
      
      - job_name: 'bearer-services'
        static_configs:
          - targets: 
            - 'bearer-service-1:13000'
            - 'bearer-service-2:13001'

# Grafana configuration
grafana:
  # Admin credentials
  adminPassword: "your-secure-grafana-password"
  
  # Persistence
  persistence:
    enabled: true
    size: 10Gi
    storageClassName: "your-storage-class"
  
  # Service configuration
  service:
    type: NodePort
    nodePort: 30300
  
  # Grafana configuration
  grafana.ini:
    server:
      domain: grafana.yourcompany.local
      root_url: "http://grafana.yourcompany.local:3000"
    
    # Corporate authentication (if using LDAP/AD)
    auth.ldap:
      enabled: true
      config_file: /etc/grafana/ldap.toml
    
    # Security settings
    security:
      admin_user: admin
      admin_password: your-secure-grafana-password
      secret_key: your-secret-key
  
  # LDAP configuration for corporate SSO
  ldap:
    enabled: true
    config: |
      [[servers]]
      host = "your-ldap-server.company.local"
      port = 389
      use_ssl = false
      start_tls = false
      ssl_skip_verify = false
      bind_dn = "CN=grafana-service,OU=Service Accounts,DC=company,DC=local"
      bind_password = "service-account-password"
      search_filter = "(sAMAccountName=%s)"
      search_base_dns = ["OU=Users,DC=company,DC=local"]
      
      [servers.attributes]
      name = "givenName"
      surname = "sn"
      username = "sAMAccountName"
      member_of = "memberOf"
      email = "mail"

# AlertManager configuration
alertmanager:
  alertmanagerSpec:
    storage:
      volumeClaimTemplate:
        spec:
          storageClassName: "your-storage-class"
          accessModes: ["ReadWriteOnce"]
          resources:
            requests:
              storage: 10Gi
    
    # Corporate notification channels
    config:
      global:
        smtp_smarthost: 'smtp.yourcompany.local:587'
        smtp_from: '<EMAIL>'
        smtp_auth_username: '<EMAIL>'
        smtp_auth_password: 'smtp-password'
      
      route:
        group_by: ['alertname', 'cluster', 'service']
        group_wait: 10s
        group_interval: 10s
        repeat_interval: 1h
        receiver: 'web.hook'
        routes:
        - match:
            severity: critical
          receiver: 'critical-alerts'
        - match:
            severity: warning
          receiver: 'warning-alerts'
      
      receivers:
      - name: 'web.hook'
        webhook_configs:
        - url: 'http://your-webhook-url/alerts'
      
      - name: 'critical-alerts'
        email_configs:
        - to: '<EMAIL>'
          subject: '🚨 Critical Alert: {{ .GroupLabels.alertname }}'
          body: |
            {{ range .Alerts }}
            Alert: {{ .Annotations.summary }}
            Description: {{ .Annotations.description }}
            {{ end }}
        
        # SMS notifications for critical alerts
        webhook_configs:
        - url: 'http://sms-gateway:7788/send-sms'
          send_resolved: true
      
      - name: 'warning-alerts'
        email_configs:
        - to: '<EMAIL>'
          subject: '⚠️ Warning Alert: {{ .GroupLabels.alertname }}'

# Node Exporter for infrastructure metrics
nodeExporter:
  enabled: true
  
# kube-state-metrics for Kubernetes metrics
kubeStateMetrics:
  enabled: true

# Prometheus Operator
prometheusOperator:
  enabled: true
  
  # Resource limits
  resources:
    requests:
      memory: 256Mi
      cpu: 100m
    limits:
      memory: 512Mi
      cpu: 200m
