{"python.defaultInterpreterPath": "./venv/bin/python", "python.linting.enabled": true, "python.linting.pylintEnabled": false, "python.linting.flake8Enabled": true, "python.linting.mypyEnabled": true, "python.formatting.provider": "black", "python.formatting.blackArgs": ["--line-length", "88"], "python.sortImports.args": ["--profile", "black"], "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports": true}, "files.exclude": {"**/__pycache__": true, "**/*.pyc": true, ".pytest_cache": true, ".mypy_cache": true, "*.egg-info": true}, "jupyter.askForKernelRestart": false, "jupyter.interactiveWindowMode": "perFile", "python.testing.pytestEnabled": true, "python.testing.unittestEnabled": false, "python.testing.pytestArgs": ["tests"], "files.associations": {"*.yml": "yaml", "*.yaml": "yaml"}}