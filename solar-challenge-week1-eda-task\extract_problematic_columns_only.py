import pandas as pd

print("🔍 Extracting Untransformed Values from Problematic Columns Only...")

# Step 1: Read the data
print("📖 Reading data...")
original_matrix = pd.read_excel('combined.xlsx', sheet_name='categorycodeandrolecode')
transformed_matrix = pd.read_excel('final_centered_format.xlsx', sheet_name='4_matrix_readable')
categories_df = pd.read_excel('combined.xlsx', sheet_name='categorycodeandcatagorydescript')

# Create category lookup
category_dict = dict(zip(categories_df['Category_code'], categories_df['Description']))

print(f"Original matrix shape: {original_matrix.shape}")
print(f"Transformed matrix shape: {transformed_matrix.shape}")

# Step 2: Identify problematic columns (those without category descriptions)
print("🔍 Identifying problematic columns...")
problematic_columns = []

for col_idx, orig_col in enumerate(original_matrix.columns):
    col_str = str(orig_col)
    
    # Check if this column has no description (problematic)
    if col_str not in category_dict:
        problematic_columns.append({
            'index': col_idx,
            'original_name': col_str,
            'transformed_name': transformed_matrix.columns[col_idx] if col_idx < len(transformed_matrix.columns) else col_str
        })

print(f"✓ Found {len(problematic_columns)} problematic columns:")
for col in problematic_columns:
    print(f"  - {col['original_name']} (index {col['index']})")

# Step 3: Extract untransformed values from problematic columns only
print("🔍 Extracting untransformed values from problematic columns...")
untransformed_by_column = {}

for col_info in problematic_columns:
    col_idx = col_info['index']
    orig_name = col_info['original_name']
    trans_name = col_info['transformed_name']
    
    print(f"Analyzing problematic column: {orig_name}")
    
    # Get all unique non-null values in this column from transformed matrix
    if col_idx < len(transformed_matrix.columns):
        column_values = transformed_matrix.iloc[:, col_idx].dropna().unique()
        
        # All values in problematic columns are likely untransformed
        untransformed_values = []
        for value in column_values:
            value_str = str(value).strip()
            if value_str and value_str != 'nan':
                untransformed_values.append(value_str)
        
        # Store results
        if untransformed_values:
            untransformed_by_column[orig_name] = {
                'original_column_name': orig_name,
                'transformed_column_name': trans_name,
                'untransformed_values': sorted(set(untransformed_values)),
                'count': len(set(untransformed_values))
            }

print(f"✓ Found untransformed values in {len(untransformed_by_column)} problematic columns")

# Step 4: Create detailed results
print("📊 Creating detailed analysis...")
detailed_data = []

for col_name, info in untransformed_by_column.items():
    for value in info['untransformed_values']:
        detailed_data.append({
            'Problematic_Column': col_name,
            'Column_Status': 'No category description found',
            'Untransformed_Value': value,
            'Issue': 'Value in unmapped category column'
        })

detailed_df = pd.DataFrame(detailed_data)

# Step 5: Create summary
summary_data = []
for col_name, info in untransformed_by_column.items():
    summary_data.append({
        'Problematic_Column': col_name,
        'Count_Untransformed_Values': info['count'],
        'Sample_Values': ', '.join(info['untransformed_values'][:5]) + ('...' if len(info['untransformed_values']) > 5 else ''),
        'Status': 'Unmapped category - no description available'
    })

summary_df = pd.DataFrame(summary_data)

# Step 6: Create individual column sheets
print("📋 Creating individual column sheets...")
column_sheets = {}

for col_name, info in untransformed_by_column.items():
    col_data = []
    for value in info['untransformed_values']:
        col_data.append({
            'Untransformed_Value': value,
            'Column_Issue': f"Column '{col_name}' has no category description",
            'Value_Issue': 'Likely missing from role lookup table'
        })
    column_sheets[col_name] = pd.DataFrame(col_data)

# Step 7: Save to Excel
print("💾 Saving problematic columns analysis...")
with pd.ExcelWriter('problematic_columns_analysis.xlsx', engine='openpyxl') as writer:
    # Summary sheet
    summary_df.to_excel(writer, sheet_name='1_SUMMARY', index=False)
    
    # Detailed list
    detailed_df.to_excel(writer, sheet_name='2_ALL_PROBLEMATIC_VALUES', index=False)
    
    # Individual column sheets
    for col_name, col_df in column_sheets.items():
        safe_sheet_name = str(col_name)[:25].replace('/', '_')
        col_df.to_excel(writer, sheet_name=f'3_{safe_sheet_name}', index=False)

print("🎉 SUCCESS! Created 'problematic_columns_analysis.xlsx'")

# Step 8: Show preview
print("\n📊 PROBLEMATIC COLUMNS ANALYSIS:")
print(f"Total problematic columns: {len(untransformed_by_column)}")
print(f"Total untransformed values in problematic columns: {len(detailed_df)}")

print("\n📈 PROBLEMATIC COLUMNS SUMMARY:")
if not summary_df.empty:
    print(summary_df.to_string(index=False))

print("\n📋 SAMPLE VALUES FROM PROBLEMATIC COLUMNS:")
if not detailed_df.empty:
    print(detailed_df.head(15).to_string(index=False))

print(f"\n📁 OUTPUT FILE: problematic_columns_analysis.xlsx")
print("• Sheet 1: Summary of problematic columns")
print("• Sheet 2: All values from problematic columns")
print("• Sheet 3+: Individual sheets for each problematic column")

print(f"\n🎯 FOCUS AREAS:")
print("These are the columns mentioned in your issue:")
print("RET, CORPE, CHAN23, and other unmapped categories")
print("All values in these columns need to be reviewed and mapped.")
