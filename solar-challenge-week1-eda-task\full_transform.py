import pandas as pd

print("🔄 Starting Excel Transformation...")

# Step 1: Read all sheets
print("📖 Reading Excel sheets...")
roles_df = pd.read_excel('combined.xlsx', sheet_name='role_codeandrolecdescription')
categories_df = pd.read_excel('combined.xlsx', sheet_name='categorycodeandcatagorydescript') 
matrix_df = pd.read_excel('combined.xlsx', sheet_name='categorycodeandrolecode')

print(f"✓ Loaded {roles_df.shape[0]} roles, {categories_df.shape[0]} categories, {matrix_df.shape} matrix")

# Step 2: Create lookup dictionaries
print("🔍 Creating lookup dictionaries...")
role_dict = dict(zip(roles_df['Role_ Code'], roles_df['Role_Name']))
category_dict = dict(zip(categories_df['Category_code'], categories_df['Description']))

# Step 3: Transform column headers
print("🏷️ Transforming column headers...")
new_columns = []
for col in matrix_df.columns:
    col_str = str(col)
    if col_str in category_dict:
        new_name = category_dict[col_str]
        new_columns.append(new_name)
        print(f"  '{col}' → '{new_name}'")
    else:
        new_columns.append(col_str)
        print(f"  '{col}' → (unchanged)")

# Create transformed dataframe
transformed_df = matrix_df.copy()
transformed_df.columns = new_columns

# Step 4: Transform cell values
print("📝 Transforming cell values...")
total_cells = transformed_df.shape[0] * transformed_df.shape[1]
transformed_count = 0

for col in transformed_df.columns:
    for idx in transformed_df.index:
        cell_value = transformed_df.at[idx, col]
        if pd.notna(cell_value):
            cell_str = str(cell_value)
            if cell_str in role_dict:
                transformed_df.at[idx, col] = role_dict[cell_str]
                transformed_count += 1

print(f"✓ Transformed {transformed_count} cell values out of {total_cells} total cells")

# Step 5: Save to new Excel file
print("💾 Saving transformed data...")
with pd.ExcelWriter('combined_transformed.xlsx', engine='openpyxl') as writer:
    # Original sheets
    roles_df.to_excel(writer, sheet_name='1_roles_original', index=False)
    categories_df.to_excel(writer, sheet_name='2_categories_original', index=False)
    matrix_df.to_excel(writer, sheet_name='3_matrix_original', index=False)
    
    # New transformed sheet
    transformed_df.to_excel(writer, sheet_name='4_TRANSFORMED_READABLE', index=False)

print("🎉 SUCCESS! Created 'combined_transformed.xlsx'")

# Step 6: Show preview
print("\n📊 PREVIEW OF TRANSFORMED SHEET:")
print(f"Shape: {transformed_df.shape}")
print("\nFirst 3 rows and 5 columns:")
print(transformed_df.iloc[:3, :5].to_string())

print("\n🔄 TRANSFORMATION SUMMARY:")
print(f"• Original matrix: {matrix_df.shape}")
print(f"• Transformed matrix: {transformed_df.shape}")
print(f"• Column headers transformed: {len([c for c in matrix_df.columns if str(c) in category_dict])}")
print(f"• Cell values transformed: {transformed_count}")
print(f"• Output file: combined_transformed.xlsx")
