import pandas as pd

print("🔄 Creating Flattened Category-Role List...")

# Step 1: Read all sheets
print("📖 Reading Excel sheets...")
roles_df = pd.read_excel('combined.xlsx', sheet_name='role_codeandrolecdescription')
categories_df = pd.read_excel('combined.xlsx', sheet_name='categorycodeandcatagorydescript') 
matrix_df = pd.read_excel('combined.xlsx', sheet_name='categorycodeandrolecode')

# Step 2: Create lookup dictionaries
print("🔍 Creating lookup dictionaries...")
role_dict = dict(zip(roles_df['Role_ Code'], roles_df['Role_Name']))
category_dict = dict(zip(categories_df['Category_code'], categories_df['Description']))

# Step 3: Create flattened list
print("📋 Creating flattened category-role list...")
flattened_data = []

for col_idx, col in enumerate(matrix_df.columns):
    col_str = str(col)
    
    # Get category description
    if col_str in category_dict:
        category_desc = category_dict[col_str]
    else:
        category_desc = col_str  # Keep original if no match
    
    print(f"Processing column {col_idx + 1}/{len(matrix_df.columns)}: {category_desc}")
    
    # Get all roles in this category
    roles_in_category = matrix_df[col].dropna().unique()
    
    for role_code in roles_in_category:
        role_str = str(role_code)
        
        # Get role description
        if role_str in role_dict:
            role_desc = role_dict[role_str]
        else:
            role_desc = role_str  # Keep original if no match
        
        # Add to flattened data
        flattened_data.append({
            'Category': category_desc,
            'Role': role_desc
        })

# Step 4: Create DataFrame
flattened_df = pd.DataFrame(flattened_data)

# Remove duplicates (same category-role combination)
flattened_df = flattened_df.drop_duplicates()

print(f"✓ Created {len(flattened_df)} unique category-role relationships")

# Step 5: Sort by category then by role
flattened_df = flattened_df.sort_values(['Category', 'Role']).reset_index(drop=True)

# Step 6: Create the transformed matrix (from previous script)
print("🔄 Creating transformed matrix...")
transformed_df = matrix_df.copy()

# Transform column headers
new_columns = []
for col in matrix_df.columns:
    col_str = str(col)
    if col_str in category_dict:
        new_columns.append(category_dict[col_str])
    else:
        new_columns.append(col_str)

transformed_df.columns = new_columns

# Transform cell values
for col in transformed_df.columns:
    for idx in transformed_df.index:
        cell_value = transformed_df.at[idx, col]
        if pd.notna(cell_value):
            cell_str = str(cell_value)
            if cell_str in role_dict:
                transformed_df.at[idx, col] = role_dict[cell_str]

# Step 7: Save to Excel with all sheets
print("💾 Saving to Excel file...")
with pd.ExcelWriter('final_combined_with_flattened.xlsx', engine='openpyxl') as writer:
    # Original sheets
    roles_df.to_excel(writer, sheet_name='1_roles_original', index=False)
    categories_df.to_excel(writer, sheet_name='2_categories_original', index=False)
    matrix_df.to_excel(writer, sheet_name='3_matrix_original', index=False)
    
    # Transformed matrix
    transformed_df.to_excel(writer, sheet_name='4_matrix_readable', index=False)
    
    # NEW: Flattened list
    flattened_df.to_excel(writer, sheet_name='5_FLATTENED_LIST', index=False)

print("🎉 SUCCESS! Created 'final_combined_with_flattened.xlsx'")

# Step 8: Show preview and statistics
print("\n📊 FLATTENED LIST PREVIEW:")
print(f"Total rows: {len(flattened_df)}")
print("\nFirst 10 rows:")
print(flattened_df.head(10).to_string(index=False))

print("\n📈 STATISTICS:")
category_counts = flattened_df['Category'].value_counts()
print(f"Categories with most roles:")
for cat, count in category_counts.head(5).items():
    print(f"  {cat}: {count} roles")

print(f"\n📁 FILE STRUCTURE:")
print("• Sheet 1: Original roles (code → name)")
print("• Sheet 2: Original categories (code → description)")  
print("• Sheet 3: Original matrix (codes)")
print("• Sheet 4: Readable matrix (descriptions)")
print("• Sheet 5: FLATTENED LIST (Category → Role pairs)")
print(f"\n✅ Total unique category-role relationships: {len(flattened_df)}")
