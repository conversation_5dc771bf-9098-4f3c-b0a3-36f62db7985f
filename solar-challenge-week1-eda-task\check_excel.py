import pandas as pd
import numpy as np

try:
    # Read the Excel file
    print("Reading Excel file...")
    df = pd.read_excel('combined.xlsx')

    print("=== EXCEL FILE ANALYSIS ===")
    print(f"Shape: {df.shape}")
    print(f"Number of rows: {df.shape[0]}")
    print(f"Number of columns: {df.shape[1]}")

    print("\n=== COLUMNS ===")
    print(df.columns.tolist())

    print("\n=== DATA TYPES ===")
    print(df.dtypes)

    print("\n=== FIRST 5 ROWS ===")
    print(df.head())

    print("\n=== LAST 5 ROWS ===")
    print(df.tail())

    print("\n=== BASIC STATISTICS ===")
    print(df.describe(include='all'))

    print("\n=== MISSING VALUES ===")
    missing = df.isnull().sum()
    if missing.sum() > 0:
        print(missing[missing > 0])
    else:
        print("No missing values found!")

    print("\n=== UNIQUE VALUES PER COLUMN ===")
    for col in df.columns:
        unique_count = df[col].nunique()
        print(f"{col}: {unique_count} unique values")
        if unique_count <= 10:
            print(f"  Values: {df[col].unique()}")
        elif unique_count <= 50:
            print(f"  Sample values: {df[col].unique()[:10]}")

    # Check if there are multiple sheets
    print("\n=== EXCEL SHEETS ===")
    excel_file = pd.ExcelFile('combined.xlsx')
    print(f"Sheet names: {excel_file.sheet_names}")

except Exception as e:
    print(f"Error reading Excel file: {e}")
    import traceback
    traceback.print_exc()
