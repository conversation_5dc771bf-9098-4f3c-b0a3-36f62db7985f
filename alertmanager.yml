# AlertManager Configuration for Corporate Environment
global:
  # SMTP configuration for corporate email
  smtp_smarthost: 'smtp.yourcompany.local:587'
  smtp_from: '<EMAIL>'
  smtp_auth_username: '<EMAIL>'
  smtp_auth_password: 'your-smtp-password'
  smtp_require_tls: true

# Templates for custom alert formatting
templates:
  - '/etc/alertmanager/templates/*.tmpl'

# Routing configuration
route:
  group_by: ['alertname', 'cluster', 'service']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'default-receiver'
  
  # Route specific alerts to different receivers
  routes:
  # Critical infrastructure alerts
  - match:
      severity: critical
    receiver: 'critical-alerts'
    group_wait: 5s
    repeat_interval: 30m
    
  # Business application alerts
  - match_re:
      job: '(java-app|sms-gateway|bearer-service)'
    receiver: 'application-alerts'
    group_wait: 30s
    repeat_interval: 2h
    
  # Database alerts
  - match:
      job: postgres-main
    receiver: 'database-alerts'
    group_wait: 15s
    repeat_interval: 1h
    
  # Warning level alerts
  - match:
      severity: warning
    receiver: 'warning-alerts'
    repeat_interval: 4h

# Inhibition rules to reduce noise
inhibit_rules:
  # Inhibit warning alerts if critical alert is firing
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'instance']

# Receiver configurations
receivers:
# Default receiver
- name: 'default-receiver'
  email_configs:
  - to: '<EMAIL>'
    subject: '🔔 Prometheus Alert: {{ .GroupLabels.alertname }}'
    html: |
      <h3>Alert Summary</h3>
      <p><strong>Alert:</strong> {{ .GroupLabels.alertname }}</p>
      <p><strong>Cluster:</strong> {{ .GroupLabels.cluster }}</p>
      <p><strong>Severity:</strong> {{ .CommonLabels.severity }}</p>
      
      <h4>Firing Alerts:</h4>
      {{ range .Alerts }}
      <p>
        <strong>{{ .Annotations.summary }}</strong><br>
        {{ .Annotations.description }}<br>
        <em>Started:</em> {{ .StartsAt.Format "2006-01-02 15:04:05" }}
      </p>
      {{ end }}

# Critical alerts - immediate notification
- name: 'critical-alerts'
  email_configs:
  - to: '<EMAIL>,<EMAIL>'
    subject: '🚨 CRITICAL ALERT: {{ .GroupLabels.alertname }}'
    html: |
      <h2 style="color: red;">🚨 CRITICAL INFRASTRUCTURE ALERT</h2>
      <p><strong>Alert:</strong> {{ .GroupLabels.alertname }}</p>
      <p><strong>Severity:</strong> CRITICAL</p>
      <p><strong>Time:</strong> {{ .CommonAnnotations.timestamp }}</p>
      
      <h3>Affected Systems:</h3>
      {{ range .Alerts }}
      <div style="border: 1px solid red; padding: 10px; margin: 5px;">
        <strong>{{ .Annotations.summary }}</strong><br>
        {{ .Annotations.description }}<br>
        <strong>Instance:</strong> {{ .Labels.instance }}<br>
        <strong>Started:</strong> {{ .StartsAt.Format "2006-01-02 15:04:05" }}
      </div>
      {{ end }}
      
      <p><strong>Action Required:</strong> Immediate investigation and resolution needed.</p>
  
  # SMS notification for critical alerts (via webhook to SMS gateway)
  webhook_configs:
  - url: 'http://host.docker.internal:7788/send-alert-sms'
    send_resolved: true
    http_config:
      basic_auth:
        username: 'alert-user'
        password: 'alert-password'
    title: 'Critical Alert: {{ .GroupLabels.alertname }}'
    text: |
      CRITICAL: {{ .GroupLabels.alertname }}
      {{ range .Alerts }}{{ .Annotations.summary }}{{ end }}
      Time: {{ .CommonAnnotations.timestamp }}

# Application-specific alerts
- name: 'application-alerts'
  email_configs:
  - to: '<EMAIL>,<EMAIL>'
    subject: '⚠️ Application Alert: {{ .GroupLabels.alertname }}'
    html: |
      <h3>Application Alert</h3>
      <p><strong>Service:</strong> {{ .GroupLabels.job }}</p>
      <p><strong>Alert:</strong> {{ .GroupLabels.alertname }}</p>
      
      {{ range .Alerts }}
      <div style="border: 1px solid orange; padding: 10px; margin: 5px;">
        <strong>{{ .Annotations.summary }}</strong><br>
        {{ .Annotations.description }}<br>
        <strong>Instance:</strong> {{ .Labels.instance }}<br>
        <strong>Started:</strong> {{ .StartsAt.Format "2006-01-02 15:04:05" }}
      </div>
      {{ end }}

# Database alerts
- name: 'database-alerts'
  email_configs:
  - to: '<EMAIL>,<EMAIL>'
    subject: '🗄️ Database Alert: {{ .GroupLabels.alertname }}'
    html: |
      <h3>Database Alert</h3>
      <p><strong>Database:</strong> {{ .GroupLabels.job }}</p>
      <p><strong>Alert:</strong> {{ .GroupLabels.alertname }}</p>
      
      {{ range .Alerts }}
      <div style="border: 1px solid blue; padding: 10px; margin: 5px;">
        <strong>{{ .Annotations.summary }}</strong><br>
        {{ .Annotations.description }}<br>
        <strong>Instance:</strong> {{ .Labels.instance }}<br>
        <strong>Started:</strong> {{ .StartsAt.Format "2006-01-02 15:04:05" }}
      </div>
      {{ end }}

# Warning alerts
- name: 'warning-alerts'
  email_configs:
  - to: '<EMAIL>'
    subject: '⚠️ Warning: {{ .GroupLabels.alertname }}'
    html: |
      <h3>Warning Alert</h3>
      <p><strong>Alert:</strong> {{ .GroupLabels.alertname }}</p>
      <p><strong>Severity:</strong> Warning</p>
      
      {{ range .Alerts }}
      <div style="border: 1px solid yellow; padding: 10px; margin: 5px;">
        <strong>{{ .Annotations.summary }}</strong><br>
        {{ .Annotations.description }}<br>
        <strong>Instance:</strong> {{ .Labels.instance }}<br>
        <strong>Started:</strong> {{ .StartsAt.Format "2006-01-02 15:04:05" }}
      </div>
      {{ end }}
