import pandas as pd
import sys

print("Starting transformation script...")
sys.stdout.flush()

try:
    print("Reading Excel file...")
    sys.stdout.flush()

    # Read all three sheets
    sheet1_roles = pd.read_excel('combined.xlsx', sheet_name='role_codeandrolecdescription')
    print("✓ Sheet 1 loaded")
    sys.stdout.flush()

    sheet2_matrix = pd.read_excel('combined.xlsx', sheet_name='categorycodeandrolecode')
    print("✓ Sheet 2 loaded")
    sys.stdout.flush()

    sheet3_categories = pd.read_excel('combined.xlsx', sheet_name='categorycodeandcatagorydescript')
    print("✓ Sheet 3 loaded")
    sys.stdout.flush()

    print("Creating lookup dictionaries...")

    # Create lookup dictionaries
    # Role code to role name mapping
    role_lookup = dict(zip(sheet1_roles['Role_ Code'], sheet1_roles['Role_Name']))

    # Category code to description mapping
    category_lookup = dict(zip(sheet3_categories['Category_code'], sheet3_categories['Description']))

    print(f"Role lookup dictionary has {len(role_lookup)} entries")
    print(f"Category lookup dictionary has {len(category_lookup)} entries")

    # Create a copy of sheet2 for transformation
    transformed_sheet = sheet2_matrix.copy()

    print("Transforming column headers...")
    # Transform column headers (category codes to descriptions)
    new_columns = []
    for col in transformed_sheet.columns:
        if str(col) in category_lookup:
            new_columns.append(category_lookup[str(col)])
        else:
            # Keep original if no match found
            new_columns.append(str(col))

    transformed_sheet.columns = new_columns

    print("Transforming cell values...")
    # Transform cell values (role codes to role names)
    for col in transformed_sheet.columns:
        transformed_sheet[col] = transformed_sheet[col].map(
            lambda x: role_lookup.get(str(x), x) if pd.notna(x) else x
        )

    print("Saving transformed data...")

    # Create a new Excel file with all sheets including the transformed one
    with pd.ExcelWriter('combined_with_transformed.xlsx', engine='openpyxl') as writer:
        # Write original sheets
        sheet1_roles.to_excel(writer, sheet_name='role_codeandrolecdescription', index=False)
        sheet2_matrix.to_excel(writer, sheet_name='categorycodeandrolecode', index=False)
        sheet3_categories.to_excel(writer, sheet_name='categorycodeandcatagorydescript', index=False)

        # Write the new transformed sheet
        transformed_sheet.to_excel(writer, sheet_name='transformed_readable', index=False)

    print("✅ SUCCESS!")
    print("Created 'combined_with_transformed.xlsx' with new sheet 'transformed_readable'")

    # Show a preview of the transformation
    print("\n=== PREVIEW OF TRANSFORMED SHEET ===")
    print("Shape:", transformed_sheet.shape)
    print("\nFirst few columns and rows:")
    print(transformed_sheet.iloc[:5, :5])

    print("\n=== COLUMN TRANSFORMATION EXAMPLES ===")
    original_cols = sheet2_matrix.columns[:10]
    new_cols = transformed_sheet.columns[:10]

    for orig, new in zip(original_cols, new_cols):
        if str(orig) != str(new):
            print(f"'{orig}' → '{new}'")
        else:
            print(f"'{orig}' → (no change)")

    print("\n=== CELL VALUE TRANSFORMATION EXAMPLES ===")
    # Show some examples of role code transformations
    sample_data = sheet2_matrix.iloc[0, :5].dropna()
    for col_idx, (col_name, value) in enumerate(sample_data.items()):
        if str(value) in role_lookup:
            transformed_value = role_lookup[str(value)]
            print(f"'{value}' → '{transformed_value}'")
        else:
            print(f"'{value}' → (no change)")
        if col_idx >= 4:  # Show only first 5 examples
            break

except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
