# Core Data Science Libraries
pandas>=1.5.0
numpy>=1.21.0
matplotlib>=3.5.0
seaborn>=0.11.0
plotly>=5.0.0
jupyter>=1.0.0

# Financial Analysis Libraries
yfinance>=0.2.0
pandas-ta>=0.3.14b
TA-Lib>=0.4.25
pynance>=0.4.0
quantlib>=1.29

# NLP and Sentiment Analysis
nltk>=3.8
textblob>=0.17.1
vaderSentiment>=3.3.2
transformers>=4.20.0
spacy>=3.4.0
gensim>=4.2.0

# Statistical Analysis
scipy>=1.9.0
scikit-learn>=1.1.0
statsmodels>=0.13.0

# Data Visualization
plotly-dash>=2.6.0
bokeh>=2.4.0
wordcloud>=1.9.0

# Web Scraping (if needed)
requests>=2.28.0
beautifulsoup4>=4.11.0

# Development and Testing
pytest>=7.1.0
pytest-cov>=3.0.0
black>=22.6.0
flake8>=5.0.0
isort>=5.10.0

# Utilities
python-dotenv>=0.20.0
tqdm>=4.64.0
openpyxl>=3.0.0

# Time Series Analysis
arch>=5.3.0
pmdarima>=2.0.0
