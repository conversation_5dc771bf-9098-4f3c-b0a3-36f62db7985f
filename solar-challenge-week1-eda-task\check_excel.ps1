# PowerShell script to read Excel file
try {
    $excel = New-Object -ComObject Excel.Application
    $excel.Visible = $false
    $workbook = $excel.Workbooks.Open("$PWD\combined.xlsx")
    $worksheet = $workbook.Worksheets.Item(1)
    
    Write-Host "=== EXCEL FILE ANALYSIS ==="
    Write-Host "Worksheet Name: $($worksheet.Name)"
    
    # Get used range
    $usedRange = $worksheet.UsedRange
    $rows = $usedRange.Rows.Count
    $cols = $usedRange.Columns.Count
    
    Write-Host "Rows: $rows"
    Write-Host "Columns: $cols"
    
    Write-Host "`n=== FIRST ROW (Headers) ==="
    for ($i = 1; $i -le $cols; $i++) {
        $cellValue = $worksheet.Cells.Item(1, $i).Value2
        Write-Host "Column $i`: $cellValue"
    }
    
    Write-Host "`n=== FIRST 5 ROWS ==="
    for ($row = 1; $row -le [Math]::Min(5, $rows); $row++) {
        $rowData = @()
        for ($col = 1; $col -le $cols; $col++) {
            $cellValue = $worksheet.Cells.Item($row, $col).Value2
            $rowData += $cellValue
        }
        Write-Host "Row $row`: $($rowData -join ' | ')"
    }
    
    $workbook.Close($false)
    $excel.Quit()
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
}
catch {
    Write-Host "Error: $($_.Exception.Message)"
    Write-Host "Excel might not be installed or COM object not available."
}
