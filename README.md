# Nova Financial Solutions - News Sentiment Analysis Project

## 🎯 Project Overview
Comprehensive analysis of financial news sentiment correlation with stock price movements to enhance predictive analytics capabilities for Nova Financial Solutions.

## 📊 Business Objective
Develop advanced data analysis capabilities to significantly boost financial forecasting accuracy and operational efficiency through:
- **Sentiment Analysis**: Quantify tone and sentiment in financial news headlines
- **Correlation Analysis**: Establish statistical correlations between news sentiment and stock price movements
- **Predictive Analytics**: Leverage insights for investment strategy recommendations

## 🗂️ Project Structure
```
nova-financial-sentiment-analysis/
├── .vscode/
│   └── settings.json
├── .github/
│   └── workflows/
│       └── unittests.yml
├── .gitignore
├── requirements.txt
├── README.md
├── src/
│   ├── __init__.py
│   ├── data_processing/
│   │   ├── __init__.py
│   │   ├── news_processor.py
│   │   └── stock_processor.py
│   ├── analysis/
│   │   ├── __init__.py
│   │   ├── sentiment_analyzer.py
│   │   ├── technical_indicators.py
│   │   └── correlation_analysis.py
│   └── visualization/
│       ├── __init__.py
│       └── plotting_utils.py
├── notebooks/
│   ├── __init__.py
│   ├── README.md
│   ├── task1_eda_analysis.ipynb
│   ├── task2_technical_analysis.ipynb
│   └── task3_correlation_analysis.ipynb
├── tests/
│   ├── __init__.py
│   ├── test_data_processing.py
│   ├── test_sentiment_analysis.py
│   └── test_correlation.py
├── scripts/
│   ├── __init__.py
│   ├── README.md
│   ├── download_stock_data.py
│   ├── run_sentiment_analysis.py
│   └── generate_correlation_report.py
├── data/
│   ├── raw/
│   │   ├── financial_news.csv
│   │   └── stock_prices/
│   ├── processed/
│   │   ├── cleaned_news.csv
│   │   ├── sentiment_scores.csv
│   │   └── aligned_data.csv
│   └── external/
├── reports/
│   ├── figures/
│   ├── task1_eda_report.md
│   ├── task2_technical_report.md
│   └── final_correlation_analysis.md
└── config/
    └── config.yaml
```

## 📋 Task Implementation

### Task 1: Git & GitHub + EDA ✅
- **Branch**: `task-1`
- **Deliverables**: 
  - Repository setup with proper structure
  - Comprehensive EDA on financial news data
  - Text analysis and topic modeling
  - Publisher and temporal analysis

### Task 2: Technical Analysis ✅
- **Branch**: `task-2`
- **Deliverables**:
  - Technical indicators using TA-Lib
  - Stock price analysis with PyNance
  - Interactive visualizations
  - Trading signal generation

### Task 3: Correlation Analysis ✅
- **Branch**: `task-3`
- **Deliverables**:
  - News sentiment analysis
  - Date alignment between datasets
  - Statistical correlation analysis
  - Predictive insights and recommendations

## 🛠️ Installation & Setup

### Prerequisites
- Python 3.8+
- Git
- Virtual environment (recommended)

### Installation
```bash
# Clone the repository
git clone <repository-url>
cd nova-financial-sentiment-analysis

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Install TA-Lib (may require additional setup)
# For Windows: Download from https://www.lfd.uci.edu/~gohlke/pythonlibs/#ta-lib
# For Linux/Mac: conda install -c conda-forge ta-lib
```

### Data Setup
1. Place your financial news data in `data/raw/financial_news.csv`
2. Place stock price data in `data/raw/stock_prices/`
3. Run data processing scripts:
```bash
python scripts/download_stock_data.py
python scripts/run_sentiment_analysis.py
```

## 🚀 Usage

### Running Individual Tasks

#### Task 1: EDA Analysis
```bash
# Open Jupyter notebook
jupyter notebook notebooks/task1_eda_analysis.ipynb

# Or run Python script
python src/data_processing/news_processor.py
```

#### Task 2: Technical Analysis
```bash
# Run technical analysis
python src/analysis/technical_indicators.py

# Generate technical charts
jupyter notebook notebooks/task2_technical_analysis.ipynb
```

#### Task 3: Correlation Analysis
```bash
# Run correlation analysis
python src/analysis/correlation_analysis.py

# Generate final report
python scripts/generate_correlation_report.py
```

### Complete Analysis Pipeline
```bash
# Run complete analysis pipeline
python scripts/run_complete_analysis.py
```

## 📊 Key Features

### Sentiment Analysis
- **TextBlob**: Polarity and subjectivity analysis
- **VADER**: Compound sentiment scoring
- **Custom Financial Lexicon**: Domain-specific sentiment analysis
- **Multi-level Aggregation**: Daily, weekly, monthly sentiment trends

### Technical Analysis
- **Moving Averages**: SMA, EMA (multiple periods)
- **Momentum Indicators**: RSI, MACD, Stochastic
- **Volatility Measures**: Bollinger Bands, ATR
- **Volume Analysis**: OBV, Volume SMA
- **Trading Signals**: Buy/sell signal generation

### Correlation Analysis
- **Temporal Correlations**: Same-day, next-day, lagged
- **Statistical Significance**: P-value testing
- **Predictive Modeling**: Next-day return prediction
- **Sector Analysis**: Industry-specific correlations

## 📈 Expected Results

### Key Performance Indicators
- **Correlation Strength**: Measure of sentiment-price relationship
- **Predictive Accuracy**: Next-day return prediction success rate
- **Signal Quality**: Trading signal performance metrics
- **Statistical Significance**: P-values for correlation coefficients

### Business Insights
- **Investment Strategies**: Sentiment-based trading recommendations
- **Risk Assessment**: News-driven volatility predictions
- **Market Timing**: Optimal entry/exit points based on sentiment
- **Portfolio Optimization**: Sentiment-aware asset allocation

## 🔧 Development

### Git Workflow
```bash
# Create feature branch
git checkout -b task-1
git add .
git commit -m "feat: initial EDA implementation"

# Daily commits (minimum 3 per day)
git commit -m "analysis: headline length distribution"
git commit -m "viz: publisher frequency charts"
git commit -m "insight: temporal pattern analysis"

# Merge to main via Pull Request
git checkout main
git merge task-1
```

### Testing
```bash
# Run unit tests
python -m pytest tests/

# Run with coverage
python -m pytest tests/ --cov=src/

# Run specific test file
python -m pytest tests/test_sentiment_analysis.py
```

### Code Quality
```bash
# Format code
black src/ tests/ scripts/

# Check linting
flake8 src/ tests/ scripts/

# Sort imports
isort src/ tests/ scripts/
```

## 📚 Documentation

### Reports
- **EDA Report**: `reports/task1_eda_report.md`
- **Technical Analysis**: `reports/task2_technical_report.md`
- **Correlation Analysis**: `reports/final_correlation_analysis.md`

### API Documentation
- **Data Processing**: `src/data_processing/README.md`
- **Analysis Modules**: `src/analysis/README.md`
- **Visualization**: `src/visualization/README.md`

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 👥 Team

**Nova Financial Solutions Data Analytics Team**
- Lead Data Analyst: [Your Name]
- Project Supervisor: [Supervisor Name]
- Technical Reviewer: [Reviewer Name]

## 📞 Contact

For questions or support, please contact:
- Email: <EMAIL>
- Slack: #data-analytics-team
- Project Board: [Link to project management tool]

---

**Nova Financial Solutions** - Enhancing Predictive Analytics Through Advanced Data Analysis
