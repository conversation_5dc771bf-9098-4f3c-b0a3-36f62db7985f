{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Task 1: Exploratory Data Analysis - Financial News Headlines\n", "\n", "## Objective\n", "Perform comprehensive EDA on financial news headlines to understand:\n", "- Text characteristics (length, complexity)\n", "- Publisher distribution and patterns\n", "- Temporal trends in news publication\n", "- Data quality and preprocessing requirements"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import plotly.express as px\n", "import plotly.graph_objects as go\n", "from plotly.subplots import make_subplots\n", "\n", "# Text analysis\n", "import re\n", "import nltk\n", "from collections import Counter\n", "from wordcloud import WordCloud\n", "\n", "# Date handling\n", "from datetime import datetime, timedelta\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set style\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"husl\")\n", "\n", "print(\"📊 Libraries imported successfully!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Data Loading and Initial Inspection"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load financial news data\n", "# Replace with your actual data path\n", "df_news = pd.read_csv('../data/raw/financial_news.csv')\n", "\n", "print(f\"📈 Dataset shape: {df_news.shape}\")\n", "print(f\"📅 Date range: {df_news['date'].min()} to {df_news['date'].max()}\")\n", "print(\"\\n🔍 Dataset Info:\")\n", "df_news.info()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Display first few rows\n", "print(\"📋 First 5 rows:\")\n", "df_news.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check for missing values\n", "print(\"❓ Missing Values Analysis:\")\n", "missing_data = df_news.isnull().sum()\n", "missing_percent = (missing_data / len(df_news)) * 100\n", "\n", "missing_df = pd.DataFrame({\n", "    'Missing Count': missing_data,\n", "    'Percentage': missing_percent\n", "})\n", "print(missing_df[missing_df['Missing Count'] > 0])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Headline Length Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calculate headline statistics\n", "df_news['headline_length'] = df_news['headline'].str.len()\n", "df_news['word_count'] = df_news['headline'].str.split().str.len()\n", "df_news['char_count_no_spaces'] = df_news['headline'].str.replace(' ', '').str.len()\n", "\n", "# Summary statistics\n", "print(\"📏 Headline Length Statistics:\")\n", "length_stats = df_news[['headline_length', 'word_count', 'char_count_no_spaces']].describe()\n", "print(length_stats)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualize headline length distribution\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "\n", "# Character length distribution\n", "axes[0,0].hist(df_news['headline_length'], bins=50, alpha=0.7, color='skyblue')\n", "axes[0,0].set_title('Distribution of Headline Character Length')\n", "axes[0,0].set_xlabel('Character Count')\n", "axes[0,0].set_ylabel('Frequency')\n", "\n", "# Word count distribution\n", "axes[0,1].hist(df_news['word_count'], bins=30, alpha=0.7, color='lightgreen')\n", "axes[0,1].set_title('Distribution of Headline Word Count')\n", "axes[0,1].set_xlabel('Word Count')\n", "axes[0,1].set_ylabel('Frequency')\n", "\n", "# Box plot for character length\n", "axes[1,0].boxplot(df_news['headline_length'])\n", "axes[1,0].set_title('Headline Character Length Box Plot')\n", "axes[1,0].set_ylabel('Character Count')\n", "\n", "# Box plot for word count\n", "axes[1,1].boxplot(df_news['word_count'])\n", "axes[1,1].set_title('Headline Word Count Box Plot')\n", "axes[1,1].set_ylabel('Word Count')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Publisher Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Publisher frequency analysis\n", "publisher_counts = df_news['publisher'].value_counts()\n", "\n", "print(f\"📰 Total number of unique publishers: {len(publisher_counts)}\")\n", "print(\"\\n🔝 Top 10 Publishers by Article Count:\")\n", "print(publisher_counts.head(10))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualize publisher distribution\n", "fig, axes = plt.subplots(1, 2, figsize=(18, 6))\n", "\n", "# Top 15 publishers\n", "top_publishers = publisher_counts.head(15)\n", "axes[0].barh(range(len(top_publishers)), top_publishers.values)\n", "axes[0].set_yticks(range(len(top_publishers)))\n", "axes[0].set_yticklabels(top_publishers.index)\n", "axes[0].set_title('Top 15 Publishers by Article Count')\n", "axes[0].set_xlabel('Number of Articles')\n", "\n", "# Publisher distribution (log scale)\n", "axes[1].hist(publisher_counts.values, bins=50, alpha=0.7)\n", "axes[1].set_yscale('log')\n", "axes[1].set_title('Distribution of Articles per Publisher (Log Scale)')\n", "axes[1].set_xlabel('Number of Articles')\n", "axes[1].set_ylabel('Number of Publishers (Log Scale)')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Temporal Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Convert date column to datetime\n", "df_news['date'] = pd.to_datetime(df_news['date'])\n", "df_news['year'] = df_news['date'].dt.year\n", "df_news['month'] = df_news['date'].dt.month\n", "df_news['day_of_week'] = df_news['date'].dt.day_name()\n", "df_news['hour'] = df_news['date'].dt.hour\n", "\n", "print(\"📅 Temporal Features Created Successfully!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Time series analysis\n", "daily_counts = df_news.groupby(df_news['date'].dt.date).size()\n", "\n", "# Plot daily article counts\n", "plt.figure(figsize=(15, 6))\n", "plt.plot(daily_counts.index, daily_counts.values, alpha=0.7)\n", "plt.title('Daily News Article Publication Trend')\n", "plt.xlabel('Date')\n", "plt.ylabel('Number of Articles')\n", "plt.xticks(rotation=45)\n", "plt.grid(True, alpha=0.3)\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(f\"📊 Average daily articles: {daily_counts.mean():.1f}\")\n", "print(f\"📈 Peak day: {daily_counts.idxmax()} with {daily_counts.max()} articles\")\n", "print(f\"📉 Lowest day: {daily_counts.idxmin()} with {daily_counts.min()} articles\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Word Frequency Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Extract all words from headlines\n", "all_words = ' '.join(df_news['headline'].astype(str)).lower()\n", "all_words = re.sub(r'[^a-zA-Z\\s]', '', all_words)  # Remove punctuation\n", "word_list = all_words.split()\n", "\n", "# Count word frequencies\n", "word_freq = Counter(word_list)\n", "common_words = word_freq.most_common(20)\n", "\n", "print(\"🔤 Top 20 Most Common Words:\")\n", "for word, count in common_words:\n", "    print(f\"{word}: {count}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create word cloud\n", "wordcloud = WordCloud(width=800, height=400, background_color='white').generate(all_words)\n", "\n", "plt.figure(figsize=(12, 6))\n", "plt.imshow(wordcloud, interpolation='bilinear')\n", "plt.axis('off')\n", "plt.title('Word Cloud of Financial News Headlines')\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Data Quality Assessment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check for duplicates\n", "duplicate_headlines = df_news['headline'].duplicated().sum()\n", "duplicate_rows = df_news.duplicated().sum()\n", "\n", "print(f\"🔄 Duplicate headlines: {duplicate_headlines}\")\n", "print(f\"🔄 Duplicate rows: {duplicate_rows}\")\n", "\n", "# Check for very short or very long headlines\n", "very_short = (df_news['headline_length'] < 10).sum()\n", "very_long = (df_news['headline_length'] > 200).sum()\n", "\n", "print(f\"📏 Very short headlines (<10 chars): {very_short}\")\n", "print(f\"📏 Very long headlines (>200 chars): {very_long}\")\n", "\n", "# Check for non-English characters or unusual patterns\n", "non_ascii = df_news['headline'].str.contains(r'[^\\x00-\\x7F]', na=False).sum()\n", "print(f\"🌐 Headlines with non-ASCII characters: {non_ascii}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. <PERSON><PERSON><PERSON> and Next Steps\n", "\n", "### Key Findings:\n", "1. **Dataset Overview**: [Add your findings]\n", "2. **Headline Characteristics**: [Add your findings]\n", "3. **Publisher Insights**: [Add your findings]\n", "4. **Te<PERSON><PERSON> Patterns**: [Add your findings]\n", "5. **Data Quality**: [Add your findings]\n", "\n", "### Preprocessing Requirements:\n", "- [ ] <PERSON><PERSON> missing values\n", "- [ ] Remove duplicates\n", "- [ ] Standardize date formats\n", "- [ ] Clean text data\n", "- [ ] Filter outliers\n", "\n", "### Next Steps:\n", "- [ ] Move to Task 2: Technical indicator analysis\n", "- [ ] Prepare data for sentiment analysis\n", "- [ ] Align news data with stock price data"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}