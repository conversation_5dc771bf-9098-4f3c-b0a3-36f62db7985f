#!/usr/bin/env python3
"""
News Data Processor for Nova Financial Solutions
Handles financial news data loading, cleaning, and preprocessing
"""

import pandas as pd
import numpy as np
import re
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class NewsProcessor:
    """Process and clean financial news data"""
    
    def __init__(self, data_path: str):
        """
        Initialize NewsProcessor
        
        Args:
            data_path: Path to the news data file
        """
        self.data_path = Path(data_path)
        self.raw_data = None
        self.processed_data = None
        self.stats = {}
        
    def load_data(self) -> pd.DataFrame:
        """Load news data from file"""
        try:
            logger.info(f"Loading news data from {self.data_path}")
            
            if self.data_path.suffix.lower() == '.csv':
                self.raw_data = pd.read_csv(self.data_path)
            elif self.data_path.suffix.lower() == '.json':
                self.raw_data = pd.read_json(self.data_path)
            else:
                raise ValueError(f"Unsupported file format: {self.data_path.suffix}")
            
            logger.info(f"Loaded {len(self.raw_data)} news articles")
            self._calculate_basic_stats()
            
            return self.raw_data
            
        except Exception as e:
            logger.error(f"Error loading data: {str(e)}")
            raise
    
    def _calculate_basic_stats(self) -> None:
        """Calculate basic statistics about the dataset"""
        if self.raw_data is None:
            return
            
        self.stats = {
            'total_articles': len(self.raw_data),
            'date_range': {
                'start': self.raw_data['date'].min() if 'date' in self.raw_data.columns else None,
                'end': self.raw_data['date'].max() if 'date' in self.raw_data.columns else None
            },
            'unique_stocks': self.raw_data['stock'].nunique() if 'stock' in self.raw_data.columns else 0,
            'unique_publishers': self.raw_data['publisher'].nunique() if 'publisher' in self.raw_data.columns else 0,
            'missing_values': self.raw_data.isnull().sum().to_dict()
        }
        
        logger.info(f"Dataset stats: {self.stats}")
    
    def clean_data(self) -> pd.DataFrame:
        """Clean and preprocess the news data"""
        if self.raw_data is None:
            raise ValueError("No data loaded. Call load_data() first.")
        
        logger.info("Starting data cleaning process...")
        
        # Create a copy for processing
        df = self.raw_data.copy()
        
        # Clean headlines
        df = self._clean_headlines(df)
        
        # Process dates
        df = self._process_dates(df)
        
        # Clean publisher information
        df = self._clean_publishers(df)
        
        # Clean stock symbols
        df = self._clean_stock_symbols(df)
        
        # Remove duplicates
        df = self._remove_duplicates(df)
        
        # Filter invalid entries
        df = self._filter_invalid_entries(df)
        
        self.processed_data = df
        logger.info(f"Data cleaning completed. {len(df)} articles remaining.")
        
        return df
    
    def _clean_headlines(self, df: pd.DataFrame) -> pd.DataFrame:
        """Clean headline text"""
        logger.info("Cleaning headlines...")
        
        # Remove null headlines
        df = df.dropna(subset=['headline'])
        
        # Convert to string and strip whitespace
        df['headline'] = df['headline'].astype(str).str.strip()
        
        # Remove very short headlines (likely invalid)
        df = df[df['headline'].str.len() >= 10]
        
        # Remove very long headlines (likely corrupted)
        df = df[df['headline'].str.len() <= 500]
        
        # Clean special characters but preserve basic punctuation
        df['headline_cleaned'] = df['headline'].apply(self._clean_text)
        
        # Calculate headline metrics
        df['headline_length'] = df['headline'].str.len()
        df['word_count'] = df['headline'].str.split().str.len()
        
        return df
    
    def _clean_text(self, text: str) -> str:
        """Clean individual text string"""
        if pd.isna(text):
            return ""
        
        # Convert to string
        text = str(text)
        
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove special characters but keep basic punctuation
        text = re.sub(r'[^\w\s\.\,\!\?\:\;\-\(\)]', '', text)
        
        # Remove multiple punctuation
        text = re.sub(r'[\.]{2,}', '.', text)
        text = re.sub(r'[\!]{2,}', '!', text)
        text = re.sub(r'[\?]{2,}', '?', text)
        
        return text.strip()
    
    def _process_dates(self, df: pd.DataFrame) -> pd.DataFrame:
        """Process and standardize date information"""
        logger.info("Processing dates...")
        
        if 'date' not in df.columns:
            logger.warning("No date column found")
            return df
        
        # Convert to datetime
        df['date'] = pd.to_datetime(df['date'], errors='coerce')
        
        # Remove rows with invalid dates
        df = df.dropna(subset=['date'])
        
        # Extract date components
        df['date_only'] = df['date'].dt.date
        df['year'] = df['date'].dt.year
        df['month'] = df['date'].dt.month
        df['day'] = df['date'].dt.day
        df['day_of_week'] = df['date'].dt.day_name()
        df['hour'] = df['date'].dt.hour
        df['is_weekend'] = df['date'].dt.weekday >= 5
        
        # Filter reasonable date range (last 10 years)
        current_date = datetime.now()
        min_date = current_date - timedelta(days=3650)  # 10 years
        df = df[df['date'] >= min_date]
        df = df[df['date'] <= current_date]
        
        return df
    
    def _clean_publishers(self, df: pd.DataFrame) -> pd.DataFrame:
        """Clean publisher information"""
        logger.info("Cleaning publishers...")
        
        if 'publisher' not in df.columns:
            logger.warning("No publisher column found")
            return df
        
        # Convert to string and clean
        df['publisher'] = df['publisher'].astype(str).str.strip()
        
        # Extract domain from email-like publishers
        df['publisher_domain'] = df['publisher'].apply(self._extract_domain)
        
        # Standardize publisher names
        df['publisher_clean'] = df['publisher'].apply(self._standardize_publisher)
        
        return df
    
    def _extract_domain(self, publisher: str) -> str:
        """Extract domain from publisher string"""
        if pd.isna(publisher) or '@' not in str(publisher):
            return str(publisher)
        
        try:
            return str(publisher).split('@')[1].lower()
        except:
            return str(publisher)
    
    def _standardize_publisher(self, publisher: str) -> str:
        """Standardize publisher names"""
        if pd.isna(publisher):
            return "Unknown"
        
        publisher = str(publisher).lower().strip()
        
        # Common standardizations
        standardizations = {
            'reuters': 'Reuters',
            'bloomberg': 'Bloomberg',
            'cnbc': 'CNBC',
            'marketwatch': 'MarketWatch',
            'yahoo': 'Yahoo Finance',
            'wsj': 'Wall Street Journal',
            'ft': 'Financial Times'
        }
        
        for key, value in standardizations.items():
            if key in publisher:
                return value
        
        return publisher.title()
    
    def _clean_stock_symbols(self, df: pd.DataFrame) -> pd.DataFrame:
        """Clean and standardize stock symbols"""
        logger.info("Cleaning stock symbols...")
        
        if 'stock' not in df.columns:
            logger.warning("No stock column found")
            return df
        
        # Convert to uppercase and strip
        df['stock'] = df['stock'].astype(str).str.upper().str.strip()
        
        # Remove invalid symbols
        df = df[df['stock'].str.match(r'^[A-Z]{1,5}$', na=False)]
        
        return df
    
    def _remove_duplicates(self, df: pd.DataFrame) -> pd.DataFrame:
        """Remove duplicate articles"""
        logger.info("Removing duplicates...")
        
        initial_count = len(df)
        
        # Remove exact duplicates
        df = df.drop_duplicates()
        
        # Remove duplicate headlines for same stock on same day
        df = df.drop_duplicates(subset=['headline', 'stock', 'date_only'], keep='first')
        
        removed_count = initial_count - len(df)
        logger.info(f"Removed {removed_count} duplicate articles")
        
        return df
    
    def _filter_invalid_entries(self, df: pd.DataFrame) -> pd.DataFrame:
        """Filter out invalid or low-quality entries"""
        logger.info("Filtering invalid entries...")
        
        initial_count = len(df)
        
        # Remove entries with missing critical information
        df = df.dropna(subset=['headline', 'stock', 'date'])
        
        # Remove headlines that are too generic or promotional
        generic_patterns = [
            r'^(advertisement|sponsored|promoted)',
            r'^(click here|read more|subscribe)',
            r'^(breaking|urgent|alert):\s*$'
        ]
        
        for pattern in generic_patterns:
            df = df[~df['headline'].str.contains(pattern, case=False, regex=True, na=False)]
        
        filtered_count = initial_count - len(df)
        logger.info(f"Filtered out {filtered_count} invalid entries")
        
        return df
    
    def get_eda_summary(self) -> Dict:
        """Generate EDA summary statistics"""
        if self.processed_data is None:
            raise ValueError("No processed data available. Run clean_data() first.")
        
        df = self.processed_data
        
        summary = {
            'dataset_overview': {
                'total_articles': len(df),
                'date_range': {
                    'start': df['date'].min(),
                    'end': df['date'].max(),
                    'days_covered': (df['date'].max() - df['date'].min()).days
                },
                'unique_stocks': df['stock'].nunique(),
                'unique_publishers': df['publisher_clean'].nunique()
            },
            'headline_analysis': {
                'avg_length': df['headline_length'].mean(),
                'median_length': df['headline_length'].median(),
                'avg_words': df['word_count'].mean(),
                'length_distribution': df['headline_length'].describe().to_dict()
            },
            'temporal_analysis': {
                'articles_per_day': df.groupby('date_only').size().describe().to_dict(),
                'articles_by_weekday': df['day_of_week'].value_counts().to_dict(),
                'articles_by_hour': df['hour'].value_counts().to_dict()
            },
            'publisher_analysis': {
                'top_publishers': df['publisher_clean'].value_counts().head(10).to_dict(),
                'publisher_distribution': df['publisher_clean'].value_counts().describe().to_dict()
            },
            'stock_analysis': {
                'top_stocks': df['stock'].value_counts().head(10).to_dict(),
                'articles_per_stock': df.groupby('stock').size().describe().to_dict()
            }
        }
        
        return summary
    
    def save_processed_data(self, output_path: str) -> None:
        """Save processed data to file"""
        if self.processed_data is None:
            raise ValueError("No processed data to save")
        
        output_path = Path(output_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        self.processed_data.to_csv(output_path, index=False)
        logger.info(f"Processed data saved to {output_path}")


# Example usage and testing
if __name__ == "__main__":
    # Example usage
    processor = NewsProcessor("data/raw/financial_news.csv")
    
    try:
        # Load and process data
        raw_data = processor.load_data()
        processed_data = processor.clean_data()
        
        # Generate EDA summary
        summary = processor.get_eda_summary()
        print("EDA Summary:")
        for section, data in summary.items():
            print(f"\n{section.upper()}:")
            print(data)
        
        # Save processed data
        processor.save_processed_data("data/processed/cleaned_news.csv")
        
    except FileNotFoundError:
        print("Sample data file not found. Please ensure data/raw/financial_news.csv exists.")
    except Exception as e:
        print(f"Error: {str(e)}")
