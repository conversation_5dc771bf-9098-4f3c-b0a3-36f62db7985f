# Grafana Datasource Provisioning
apiVersion: 1

datasources:
  # Prometheus datasource
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true
    jsonData:
      timeInterval: "15s"
      queryTimeout: "60s"
      httpMethod: "POST"
    secureJsonData: {}

  # Loki datasource (if you have Loki running)
  - name: Loki
    type: loki
    access: proxy
    url: http://host.docker.internal:3100
    editable: true
    jsonData:
      maxLines: 1000
      timeout: 60
      derivedFields:
        - datasourceUid: prometheus
          matcherRegex: "traceID=(\\w+)"
          name: TraceID
          url: "$${__value.raw}"

  # PostgreSQL datasource for business metrics
  - name: PostgreSQL-Business
    type: postgres
    access: proxy
    url: host.docker.internal:5432
    database: your_business_database
    user: grafana_readonly
    editable: true
    jsonData:
      sslmode: "disable"
      maxOpenConns: 10
      maxIdleConns: 2
      connMaxLifetime: 14400
      postgresVersion: 1300
      timescaledb: false
    secureJsonData:
      password: "your_readonly_password"
