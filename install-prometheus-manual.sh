#!/bin/bash

# Prometheus Manual Installation Script for Corporate Environment
# Download on Windows and transfer via WinSCP

set -e

# Configuration
PROMETHEUS_VERSION="2.47.0"
GRAFANA_VERSION="10.1.0"
ALERTMANAGER_VERSION="0.26.0"
NODE_EXPORTER_VERSION="1.6.1"

PROMETHEUS_USER="prometheus"
PROMETHEUS_HOME="/opt/prometheus"
GRAFANA_HOME="/opt/grafana"

echo "🚀 Starting Prometheus Installation for Corporate Environment"

# Create prometheus user
echo "👤 Creating prometheus user..."
sudo useradd --no-create-home --shell /bin/false $PROMETHEUS_USER

# Create directories
echo "📁 Creating directories..."
sudo mkdir -p $PROMETHEUS_HOME/{bin,data,config,rules}
sudo mkdir -p /etc/prometheus
sudo mkdir -p /var/lib/prometheus
sudo mkdir -p /var/log/prometheus

# Download and install Prometheus (download these on Windows first)
echo "📦 Installing Prometheus..."
cd /tmp

# Extract Prometheus (assuming you've transferred the tar.gz file)
if [ -f "prometheus-${PROMETHEUS_VERSION}.linux-amd64.tar.gz" ]; then
    tar xzf prometheus-${PROMETHEUS_VERSION}.linux-amd64.tar.gz
    sudo cp prometheus-${PROMETHEUS_VERSION}.linux-amd64/prometheus $PROMETHEUS_HOME/bin/
    sudo cp prometheus-${PROMETHEUS_VERSION}.linux-amd64/promtool $PROMETHEUS_HOME/bin/
    sudo cp -r prometheus-${PROMETHEUS_VERSION}.linux-amd64/consoles $PROMETHEUS_HOME/
    sudo cp -r prometheus-${PROMETHEUS_VERSION}.linux-amd64/console_libraries $PROMETHEUS_HOME/
else
    echo "❌ Prometheus binary not found. Please download and transfer:"
    echo "https://github.com/prometheus/prometheus/releases/download/v${PROMETHEUS_VERSION}/prometheus-${PROMETHEUS_VERSION}.linux-amd64.tar.gz"
    exit 1
fi

# Install AlertManager
echo "📦 Installing AlertManager..."
if [ -f "alertmanager-${ALERTMANAGER_VERSION}.linux-amd64.tar.gz" ]; then
    tar xzf alertmanager-${ALERTMANAGER_VERSION}.linux-amd64.tar.gz
    sudo cp alertmanager-${ALERTMANAGER_VERSION}.linux-amd64/alertmanager /usr/local/bin/
    sudo cp alertmanager-${ALERTMANAGER_VERSION}.linux-amd64/amtool /usr/local/bin/
else
    echo "❌ AlertManager binary not found. Please download and transfer:"
    echo "https://github.com/prometheus/alertmanager/releases/download/v${ALERTMANAGER_VERSION}/alertmanager-${ALERTMANAGER_VERSION}.linux-amd64.tar.gz"
fi

# Install Node Exporter
echo "📦 Installing Node Exporter..."
if [ -f "node_exporter-${NODE_EXPORTER_VERSION}.linux-amd64.tar.gz" ]; then
    tar xzf node_exporter-${NODE_EXPORTER_VERSION}.linux-amd64.tar.gz
    sudo cp node_exporter-${NODE_EXPORTER_VERSION}.linux-amd64/node_exporter /usr/local/bin/
else
    echo "❌ Node Exporter binary not found. Please download and transfer:"
    echo "https://github.com/prometheus/node_exporter/releases/download/v${NODE_EXPORTER_VERSION}/node_exporter-${NODE_EXPORTER_VERSION}.linux-amd64.tar.gz"
fi

# Set permissions
echo "🔐 Setting permissions..."
sudo chown -R $PROMETHEUS_USER:$PROMETHEUS_USER $PROMETHEUS_HOME
sudo chown -R $PROMETHEUS_USER:$PROMETHEUS_USER /var/lib/prometheus
sudo chown -R $PROMETHEUS_USER:$PROMETHEUS_USER /var/log/prometheus
sudo chmod +x $PROMETHEUS_HOME/bin/*
sudo chmod +x /usr/local/bin/alertmanager
sudo chmod +x /usr/local/bin/amtool
sudo chmod +x /usr/local/bin/node_exporter

# Create Prometheus configuration
echo "⚙️ Creating Prometheus configuration..."
sudo tee /etc/prometheus/prometheus.yml > /dev/null <<EOF
global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'production'
    environment: 'corporate'

rule_files:
  - "/etc/prometheus/rules/*.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - localhost:9093

scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Node Exporter for system metrics
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['localhost:9100']

  # Your Java applications
  - job_name: 'java-applications'
    static_configs:
      - targets: 
        - 'your-app-server:8007'
        - 'your-app-server:9899'
        - 'your-app-server:5555'
    scrape_interval: 30s

  # SMS Gateway monitoring
  - job_name: 'sms-gateway'
    static_configs:
      - targets: ['your-sms-server:7788']

  # Bearer services
  - job_name: 'bearer-services'
    static_configs:
      - targets: 
        - 'your-bearer-server:13000'
        - 'your-bearer-server:13001'

  # Alloy integration
  - job_name: 'alloy'
    static_configs:
      - targets: ['your-alloy-server:12345']

  # Database monitoring (if postgres_exporter is installed)
  - job_name: 'postgres'
    static_configs:
      - targets: ['localhost:9187']

  # Blackbox exporter for endpoint monitoring
  - job_name: 'blackbox'
    metrics_path: /probe
    params:
      module: [http_2xx]
    static_configs:
      - targets:
        - http://your-app-server:8007/health
        - http://your-sms-server:7788/status
        - http://your-bearer-server:13000/health
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: localhost:9115
EOF

# Create systemd service for Prometheus
echo "🔧 Creating Prometheus systemd service..."
sudo tee /etc/systemd/system/prometheus.service > /dev/null <<EOF
[Unit]
Description=Prometheus Server
Documentation=https://prometheus.io/docs/
After=network-online.target

[Service]
User=$PROMETHEUS_USER
Restart=on-failure
ExecStart=$PROMETHEUS_HOME/bin/prometheus \\
  --config.file=/etc/prometheus/prometheus.yml \\
  --storage.tsdb.path=/var/lib/prometheus \\
  --web.console.templates=$PROMETHEUS_HOME/consoles \\
  --web.console.libraries=$PROMETHEUS_HOME/console_libraries \\
  --web.listen-address=0.0.0.0:9090 \\
  --web.external-url=http://prometheus.yourcompany.local:9090 \\
  --storage.tsdb.retention.time=30d \\
  --storage.tsdb.retention.size=50GB

[Install]
WantedBy=multi-user.target
EOF

# Create systemd service for Node Exporter
echo "🔧 Creating Node Exporter systemd service..."
sudo tee /etc/systemd/system/node-exporter.service > /dev/null <<EOF
[Unit]
Description=Node Exporter
After=network.target

[Service]
User=node-exporter
Group=node-exporter
Type=simple
ExecStart=/usr/local/bin/node_exporter \\
  --web.listen-address=:9100 \\
  --path.procfs=/proc \\
  --path.sysfs=/sys \\
  --collector.filesystem.ignored-mount-points="^/(sys|proc|dev|host|etc|rootfs/var/lib/docker/containers|rootfs/var/lib/docker/overlay2|rootfs/run/docker/netns|rootfs/var/lib/docker/aufs)($$|/)"

[Install]
WantedBy=multi-user.target
EOF

# Create node-exporter user
sudo useradd --no-create-home --shell /bin/false node-exporter

# Create AlertManager configuration
echo "⚙️ Creating AlertManager configuration..."
sudo mkdir -p /etc/alertmanager
sudo tee /etc/alertmanager/alertmanager.yml > /dev/null <<EOF
global:
  smtp_smarthost: 'smtp.yourcompany.local:587'
  smtp_from: '<EMAIL>'
  smtp_auth_username: '<EMAIL>'
  smtp_auth_password: 'your-smtp-password'

route:
  group_by: ['alertname']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'web.hook'

receivers:
- name: 'web.hook'
  email_configs:
  - to: '<EMAIL>'
    subject: 'Alert: {{ .GroupLabels.alertname }}'
    body: |
      {{ range .Alerts }}
      Alert: {{ .Annotations.summary }}
      Description: {{ .Annotations.description }}
      {{ end }}
EOF

# Create AlertManager systemd service
sudo tee /etc/systemd/system/alertmanager.service > /dev/null <<EOF
[Unit]
Description=AlertManager
After=network.target

[Service]
User=$PROMETHEUS_USER
Group=$PROMETHEUS_USER
Type=simple
ExecStart=/usr/local/bin/alertmanager \\
  --config.file=/etc/alertmanager/alertmanager.yml \\
  --storage.path=/var/lib/alertmanager \\
  --web.external-url=http://alertmanager.yourcompany.local:9093

[Install]
WantedBy=multi-user.target
EOF

# Create alert rules directory and sample rules
sudo mkdir -p /etc/prometheus/rules
sudo tee /etc/prometheus/rules/infrastructure.yml > /dev/null <<EOF
groups:
- name: infrastructure
  rules:
  - alert: InstanceDown
    expr: up == 0
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: "Instance {{ \$labels.instance }} down"
      description: "{{ \$labels.instance }} has been down for more than 5 minutes."

  - alert: HighCPUUsage
    expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High CPU usage on {{ \$labels.instance }}"
      description: "CPU usage is above 80% for more than 5 minutes."

  - alert: HighMemoryUsage
    expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes * 100 > 90
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: "High memory usage on {{ \$labels.instance }}"
      description: "Memory usage is above 90% for more than 5 minutes."
EOF

# Set permissions for AlertManager
sudo mkdir -p /var/lib/alertmanager
sudo chown -R $PROMETHEUS_USER:$PROMETHEUS_USER /etc/alertmanager
sudo chown -R $PROMETHEUS_USER:$PROMETHEUS_USER /var/lib/alertmanager

# Reload systemd and start services
echo "🚀 Starting services..."
sudo systemctl daemon-reload
sudo systemctl enable prometheus
sudo systemctl enable node-exporter
sudo systemctl enable alertmanager

sudo systemctl start prometheus
sudo systemctl start node-exporter
sudo systemctl start alertmanager

echo "✅ Prometheus installation completed!"
echo ""
echo "📊 Access URLs:"
echo "Prometheus: http://your-server:9090"
echo "AlertManager: http://your-server:9093"
echo "Node Exporter: http://your-server:9100"
echo ""
echo "📝 Next steps:"
echo "1. Configure firewall rules for ports 9090, 9093, 9100"
echo "2. Install Grafana separately"
echo "3. Configure your applications to expose metrics"
echo "4. Set up reverse proxy for corporate access"
echo ""
echo "🔍 Check service status:"
echo "sudo systemctl status prometheus"
echo "sudo systemctl status node-exporter"
echo "sudo systemctl status alertmanager"
