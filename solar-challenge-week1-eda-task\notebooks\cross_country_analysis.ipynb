{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load cleaned data\n", "benin = pd.read_csv(\"../data/processed/benin_clean.csv\")\n", "sierra = pd.read_csv(\"../data/processed/sierra_leone_clean.csv\")\n", "togo = pd.read_csv(\"../data/processed/togo_clean.csv\")\n", "\n", "# Add country labels\n", "benin['Country'] = 'Benin'\n", "sierra['Country'] = 'Sierra Leone'\n", "togo['Country'] = 'Togo'\n", "\n", "# Combine datasets\n", "combined = pd.concat([benin, sierra, togo], ignore_index=True)\n", "\n", "# 1. Comparative boxplots\n", "plt.figure(figsize=(12, 6))\n", "sns.boxplot(data=combined, x='Country', y='GHI')\n", "plt.title('GHI Distribution by Country')\n", "plt.ylabel('GHI (W/m²)')\n", "plt.tight_layout()\n", "plt.savefig('../figures/cross_country_ghi_boxplot.png')\n", "plt.show()\n", "\n", "# 2. Summary table\n", "summary_table = combined.groupby('Country')[['GHI', 'DNI', 'DHI', 'Tamb']].agg(['mean', 'std'])\n", "print(summary_table)\n", "\n", "# 3. ANOVA test (optional)\n", "from scipy.stats import f_oneway\n", "f_stat, p_value = f_oneway(\n", "    benin['GHI'],\n", "    sierra['GHI'],\n", "    togo['GHI']\n", ")\n", "print(f\"\\nANOVA Results for GHI:\\nF-statistic: {f_stat:.2f}, p-value: {p_value:.4f}\")"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}