#!/usr/bin/env python3
"""
Task 2: Technical Analysis Implementation for Nova Financial Solutions
Using yfinance data for technical indicators calculation
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import talib
import warnings
warnings.filterwarnings('ignore')

class TechnicalAnalyzer:
    """Technical Analysis class for stock data processing"""
    
    def __init__(self, data_path):
        """Initialize with stock data path"""
        self.data_path = data_path
        self.stock_data = {}
        self.technical_indicators = {}
        
    def load_stock_data(self, symbols=['AAPL', 'TSLA', 'MSFT', 'GOOG', 'AMZN', 'META', 'NVDA']):
        """Load stock data for multiple symbols"""
        print("📈 Loading stock data...")
        
        for symbol in symbols:
            try:
                file_path = f"{self.data_path}/{symbol}_historical_data.csv"
                df = pd.read_csv(file_path)
                df['Date'] = pd.to_datetime(df['Date'])
                df.set_index('Date', inplace=True)
                df.sort_index(inplace=True)
                
                # Basic data validation
                df = df.dropna()
                
                self.stock_data[symbol] = df
                print(f"✅ Loaded {symbol}: {len(df)} records from {df.index.min()} to {df.index.max()}")
                
            except FileNotFoundError:
                print(f"❌ File not found for {symbol}")
            except Exception as e:
                print(f"❌ Error loading {symbol}: {str(e)}")
    
    def calculate_technical_indicators(self, symbol):
        """Calculate comprehensive technical indicators for a stock"""
        if symbol not in self.stock_data:
            print(f"❌ No data available for {symbol}")
            return None
            
        df = self.stock_data[symbol].copy()
        
        print(f"🔧 Calculating technical indicators for {symbol}...")
        
        # Price-based indicators
        df['SMA_20'] = talib.SMA(df['Close'], timeperiod=20)
        df['SMA_50'] = talib.SMA(df['Close'], timeperiod=50)
        df['SMA_200'] = talib.SMA(df['Close'], timeperiod=200)
        df['EMA_12'] = talib.EMA(df['Close'], timeperiod=12)
        df['EMA_26'] = talib.EMA(df['Close'], timeperiod=26)
        
        # Bollinger Bands
        df['BB_Upper'], df['BB_Middle'], df['BB_Lower'] = talib.BBANDS(
            df['Close'], timeperiod=20, nbdevup=2, nbdevdn=2, matype=0
        )
        
        # Momentum Indicators
        df['RSI'] = talib.RSI(df['Close'], timeperiod=14)
        df['MACD'], df['MACD_Signal'], df['MACD_Hist'] = talib.MACD(
            df['Close'], fastperiod=12, slowperiod=26, signalperiod=9
        )
        
        # Stochastic Oscillator
        df['Stoch_K'], df['Stoch_D'] = talib.STOCH(
            df['High'], df['Low'], df['Close'], 
            fastk_period=14, slowk_period=3, slowd_period=3
        )
        
        # Volume Indicators
        df['Volume_SMA'] = talib.SMA(df['Volume'], timeperiod=20)
        df['OBV'] = talib.OBV(df['Close'], df['Volume'])
        
        # Volatility Indicators
        df['ATR'] = talib.ATR(df['High'], df['Low'], df['Close'], timeperiod=14)
        
        # Price Change Calculations
        df['Daily_Return'] = df['Close'].pct_change()
        df['Price_Change'] = df['Close'].diff()
        df['Price_Change_Pct'] = (df['Close'] / df['Close'].shift(1) - 1) * 100
        
        # Support and Resistance Levels
        df['Resistance'] = df['High'].rolling(window=20).max()
        df['Support'] = df['Low'].rolling(window=20).min()
        
        # Trend Analysis
        df['Trend_SMA'] = np.where(df['Close'] > df['SMA_50'], 'Bullish', 'Bearish')
        df['Golden_Cross'] = np.where(df['SMA_50'] > df['SMA_200'], 1, 0)
        
        self.technical_indicators[symbol] = df
        
        print(f"✅ Technical indicators calculated for {symbol}")
        return df
    
    def create_technical_chart(self, symbol, start_date=None, end_date=None):
        """Create comprehensive technical analysis chart"""
        if symbol not in self.technical_indicators:
            self.calculate_technical_indicators(symbol)
            
        df = self.technical_indicators[symbol]
        
        # Filter by date range if provided
        if start_date:
            df = df[df.index >= start_date]
        if end_date:
            df = df[df.index <= end_date]
            
        # Create subplots
        fig = make_subplots(
            rows=4, cols=1,
            shared_xaxes=True,
            vertical_spacing=0.05,
            subplot_titles=(
                f'{symbol} Stock Price with Technical Indicators',
                'MACD',
                'RSI',
                'Volume'
            ),
            row_heights=[0.5, 0.2, 0.15, 0.15]
        )
        
        # Main price chart with indicators
        fig.add_trace(
            go.Candlestick(
                x=df.index,
                open=df['Open'],
                high=df['High'],
                low=df['Low'],
                close=df['Close'],
                name='Price'
            ),
            row=1, col=1
        )
        
        # Moving Averages
        fig.add_trace(
            go.Scatter(x=df.index, y=df['SMA_20'], name='SMA 20', line=dict(color='orange')),
            row=1, col=1
        )
        fig.add_trace(
            go.Scatter(x=df.index, y=df['SMA_50'], name='SMA 50', line=dict(color='blue')),
            row=1, col=1
        )
        fig.add_trace(
            go.Scatter(x=df.index, y=df['SMA_200'], name='SMA 200', line=dict(color='red')),
            row=1, col=1
        )
        
        # Bollinger Bands
        fig.add_trace(
            go.Scatter(x=df.index, y=df['BB_Upper'], name='BB Upper', line=dict(color='gray', dash='dash')),
            row=1, col=1
        )
        fig.add_trace(
            go.Scatter(x=df.index, y=df['BB_Lower'], name='BB Lower', line=dict(color='gray', dash='dash')),
            row=1, col=1
        )
        
        # MACD
        fig.add_trace(
            go.Scatter(x=df.index, y=df['MACD'], name='MACD', line=dict(color='blue')),
            row=2, col=1
        )
        fig.add_trace(
            go.Scatter(x=df.index, y=df['MACD_Signal'], name='Signal', line=dict(color='red')),
            row=2, col=1
        )
        fig.add_trace(
            go.Bar(x=df.index, y=df['MACD_Hist'], name='Histogram'),
            row=2, col=1
        )
        
        # RSI
        fig.add_trace(
            go.Scatter(x=df.index, y=df['RSI'], name='RSI', line=dict(color='purple')),
            row=3, col=1
        )
        fig.add_hline(y=70, line_dash="dash", line_color="red", row=3, col=1)
        fig.add_hline(y=30, line_dash="dash", line_color="green", row=3, col=1)
        
        # Volume
        fig.add_trace(
            go.Bar(x=df.index, y=df['Volume'], name='Volume', marker_color='lightblue'),
            row=4, col=1
        )
        fig.add_trace(
            go.Scatter(x=df.index, y=df['Volume_SMA'], name='Volume SMA', line=dict(color='darkblue')),
            row=4, col=1
        )
        
        # Update layout
        fig.update_layout(
            title=f'{symbol} Technical Analysis Dashboard',
            xaxis_rangeslider_visible=False,
            height=800,
            showlegend=True
        )
        
        return fig
    
    def generate_trading_signals(self, symbol):
        """Generate trading signals based on technical indicators"""
        if symbol not in self.technical_indicators:
            self.calculate_technical_indicators(symbol)
            
        df = self.technical_indicators[symbol].copy()
        
        # Initialize signals
        df['Signal'] = 0
        df['Signal_Reason'] = ''
        
        # RSI Signals
        rsi_oversold = df['RSI'] < 30
        rsi_overbought = df['RSI'] > 70
        
        # MACD Signals
        macd_bullish = (df['MACD'] > df['MACD_Signal']) & (df['MACD'].shift(1) <= df['MACD_Signal'].shift(1))
        macd_bearish = (df['MACD'] < df['MACD_Signal']) & (df['MACD'].shift(1) >= df['MACD_Signal'].shift(1))
        
        # Moving Average Signals
        golden_cross = (df['SMA_50'] > df['SMA_200']) & (df['SMA_50'].shift(1) <= df['SMA_200'].shift(1))
        death_cross = (df['SMA_50'] < df['SMA_200']) & (df['SMA_50'].shift(1) >= df['SMA_200'].shift(1))
        
        # Price above/below key MAs
        price_above_sma20 = df['Close'] > df['SMA_20']
        price_below_sma20 = df['Close'] < df['SMA_20']
        
        # Combine signals
        buy_signals = (rsi_oversold & price_above_sma20) | macd_bullish | golden_cross
        sell_signals = (rsi_overbought & price_below_sma20) | macd_bearish | death_cross
        
        df.loc[buy_signals, 'Signal'] = 1
        df.loc[sell_signals, 'Signal'] = -1
        
        # Add signal reasons
        df.loc[rsi_oversold & price_above_sma20, 'Signal_Reason'] += 'RSI_Oversold+'
        df.loc[rsi_overbought & price_below_sma20, 'Signal_Reason'] += 'RSI_Overbought+'
        df.loc[macd_bullish, 'Signal_Reason'] += 'MACD_Bullish+'
        df.loc[macd_bearish, 'Signal_Reason'] += 'MACD_Bearish+'
        df.loc[golden_cross, 'Signal_Reason'] += 'Golden_Cross+'
        df.loc[death_cross, 'Signal_Reason'] += 'Death_Cross+'
        
        return df
    
    def calculate_performance_metrics(self, symbol):
        """Calculate performance metrics for the stock"""
        if symbol not in self.technical_indicators:
            self.calculate_technical_indicators(symbol)
            
        df = self.technical_indicators[symbol]
        
        metrics = {
            'Symbol': symbol,
            'Total_Return_Pct': ((df['Close'].iloc[-1] / df['Close'].iloc[0]) - 1) * 100,
            'Annualized_Return_Pct': (((df['Close'].iloc[-1] / df['Close'].iloc[0]) ** (252 / len(df))) - 1) * 100,
            'Volatility_Pct': df['Daily_Return'].std() * np.sqrt(252) * 100,
            'Sharpe_Ratio': (df['Daily_Return'].mean() / df['Daily_Return'].std()) * np.sqrt(252),
            'Max_Drawdown_Pct': ((df['Close'] / df['Close'].cummax()) - 1).min() * 100,
            'Current_Price': df['Close'].iloc[-1],
            'Current_RSI': df['RSI'].iloc[-1],
            'Current_MACD': df['MACD'].iloc[-1],
            'Days_Above_SMA200': (df['Close'] > df['SMA_200']).sum(),
            'Total_Days': len(df)
        }
        
        return metrics

# Example usage and testing
if __name__ == "__main__":
    # Initialize analyzer
    analyzer = TechnicalAnalyzer('./yfinance_extracted/yfinance_data')
    
    # Load data
    analyzer.load_stock_data(['AAPL', 'TSLA', 'MSFT'])
    
    # Calculate indicators for AAPL
    aapl_data = analyzer.calculate_technical_indicators('AAPL')
    
    # Generate trading signals
    aapl_signals = analyzer.generate_trading_signals('AAPL')
    
    # Calculate performance metrics
    aapl_metrics = analyzer.calculate_performance_metrics('AAPL')
    
    print("\n📊 AAPL Performance Metrics:")
    for key, value in aapl_metrics.items():
        if isinstance(value, float):
            print(f"{key}: {value:.2f}")
        else:
            print(f"{key}: {value}")
    
    # Create technical chart (last 2 years)
    recent_date = aapl_data.index.max()
    start_date = recent_date - pd.DateOffset(years=2)
    
    fig = analyzer.create_technical_chart('AAPL', start_date=start_date)
    fig.show()
    
    print("\n✅ Technical analysis completed!")
    print("📈 Chart displayed with technical indicators")
    print("🔧 Ready for Task 3: Correlation Analysis with news sentiment")
