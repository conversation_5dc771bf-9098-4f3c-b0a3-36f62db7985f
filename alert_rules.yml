# Prometheus Alert Rules for VM Infrastructure
groups:
# Infrastructure alerts
- name: infrastructure
  rules:
  # Instance down
  - alert: InstanceDown
    expr: up == 0
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: "Instance {{ $labels.instance }} is down"
      description: "{{ $labels.instance }} of job {{ $labels.job }} has been down for more than 5 minutes."

  # High CPU usage
  - alert: HighCPUUsage
    expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High CPU usage on {{ $labels.instance }}"
      description: "CPU usage is above 80% (current value: {{ $value }}%) for more than 5 minutes."

  # Critical CPU usage
  - alert: CriticalCPUUsage
    expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 95
    for: 2m
    labels:
      severity: critical
    annotations:
      summary: "Critical CPU usage on {{ $labels.instance }}"
      description: "CPU usage is above 95% (current value: {{ $value }}%) for more than 2 minutes."

  # High memory usage
  - alert: HighMemoryUsage
    expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes * 100 > 85
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High memory usage on {{ $labels.instance }}"
      description: "Memory usage is above 85% (current value: {{ $value }}%) for more than 5 minutes."

  # Critical memory usage
  - alert: CriticalMemoryUsage
    expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes * 100 > 95
    for: 2m
    labels:
      severity: critical
    annotations:
      summary: "Critical memory usage on {{ $labels.instance }}"
      description: "Memory usage is above 95% (current value: {{ $value }}%) for more than 2 minutes."

  # High disk usage
  - alert: HighDiskUsage
    expr: (node_filesystem_size_bytes{fstype!="tmpfs"} - node_filesystem_free_bytes{fstype!="tmpfs"}) / node_filesystem_size_bytes{fstype!="tmpfs"} * 100 > 85
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High disk usage on {{ $labels.instance }}"
      description: "Disk usage is above 85% (current value: {{ $value }}%) on {{ $labels.mountpoint }}."

  # Critical disk usage
  - alert: CriticalDiskUsage
    expr: (node_filesystem_size_bytes{fstype!="tmpfs"} - node_filesystem_free_bytes{fstype!="tmpfs"}) / node_filesystem_size_bytes{fstype!="tmpfs"} * 100 > 95
    for: 2m
    labels:
      severity: critical
    annotations:
      summary: "Critical disk usage on {{ $labels.instance }}"
      description: "Disk usage is above 95% (current value: {{ $value }}%) on {{ $labels.mountpoint }}."

  # High load average
  - alert: HighLoadAverage
    expr: node_load15 > 2
    for: 10m
    labels:
      severity: warning
    annotations:
      summary: "High load average on {{ $labels.instance }}"
      description: "Load average is {{ $value }} for more than 10 minutes."

# Application alerts
- name: applications
  rules:
  # Java application down
  - alert: JavaApplicationDown
    expr: up{job=~"java-app.*"} == 0
    for: 2m
    labels:
      severity: critical
    annotations:
      summary: "Java application {{ $labels.job }} is down"
      description: "Java application {{ $labels.job }} on {{ $labels.instance }} has been down for more than 2 minutes."

  # SMS Gateway down
  - alert: SMSGatewayDown
    expr: up{job="sms-gateway"} == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "SMS Gateway is down"
      description: "SMS Gateway on {{ $labels.instance }} has been down for more than 1 minute."

  # Bearer service down
  - alert: BearerServiceDown
    expr: up{job=~"bearer-service.*"} == 0
    for: 2m
    labels:
      severity: critical
    annotations:
      summary: "Bearer service {{ $labels.job }} is down"
      description: "Bearer service {{ $labels.job }} on {{ $labels.instance }} has been down for more than 2 minutes."

  # High HTTP error rate
  - alert: HighHTTPErrorRate
    expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) * 100 > 5
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High HTTP error rate on {{ $labels.instance }}"
      description: "HTTP error rate is {{ $value }}% for more than 5 minutes."

  # High response time
  - alert: HighResponseTime
    expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High response time on {{ $labels.instance }}"
      description: "95th percentile response time is {{ $value }}s for more than 5 minutes."

# Database alerts
- name: database
  rules:
  # PostgreSQL down
  - alert: PostgreSQLDown
    expr: up{job="postgres-main"} == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "PostgreSQL database is down"
      description: "PostgreSQL database on {{ $labels.instance }} has been down for more than 1 minute."

  # High database connections
  - alert: HighDatabaseConnections
    expr: pg_stat_database_numbackends / pg_settings_max_connections * 100 > 80
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High database connections on {{ $labels.instance }}"
      description: "Database connections are at {{ $value }}% of maximum."

  # Database replication lag
  - alert: DatabaseReplicationLag
    expr: pg_replication_lag_seconds > 300
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "Database replication lag on {{ $labels.instance }}"
      description: "Database replication lag is {{ $value }} seconds."

# Business-specific alerts
- name: business
  rules:
  # High transaction failure rate (based on your business metrics)
  - alert: HighTransactionFailureRate
    expr: rate(business_transactions_failed_total[5m]) / rate(business_transactions_total[5m]) * 100 > 10
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High transaction failure rate"
      description: "Transaction failure rate is {{ $value }}% for more than 5 minutes."

  # Low transaction volume
  - alert: LowTransactionVolume
    expr: rate(business_transactions_total[5m]) < 10
    for: 10m
    labels:
      severity: warning
    annotations:
      summary: "Low transaction volume"
      description: "Transaction rate is {{ $value }} transactions per second for more than 10 minutes."

  # High voucher expiry rate
  - alert: HighVoucherExpiryRate
    expr: business_vouchers_expiring_soon > 1000
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High number of vouchers expiring soon"
      description: "{{ $value }} vouchers are expiring within the next 7 days."

# Container alerts (if using Docker)
- name: containers
  rules:
  # Container down
  - alert: ContainerDown
    expr: absent(container_last_seen{name!=""})
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: "Container {{ $labels.name }} is down"
      description: "Container {{ $labels.name }} has been down for more than 5 minutes."

  # High container CPU usage
  - alert: HighContainerCPU
    expr: rate(container_cpu_usage_seconds_total{name!=""}[5m]) * 100 > 80
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High CPU usage in container {{ $labels.name }}"
      description: "Container {{ $labels.name }} CPU usage is {{ $value }}%."

  # High container memory usage
  - alert: HighContainerMemory
    expr: container_memory_usage_bytes{name!=""} / container_spec_memory_limit_bytes{name!=""} * 100 > 85
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High memory usage in container {{ $labels.name }}"
      description: "Container {{ $labels.name }} memory usage is {{ $value }}%."
