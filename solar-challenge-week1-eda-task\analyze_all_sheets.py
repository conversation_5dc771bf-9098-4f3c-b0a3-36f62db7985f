import pandas as pd

try:
    # Read all sheets from the Excel file
    excel_file = pd.ExcelFile('combined.xlsx')
    print("=== COMPLETE EXCEL FILE ANALYSIS ===")
    print(f"Total sheets: {len(excel_file.sheet_names)}")
    print(f"Sheet names: {excel_file.sheet_names}")
    
    for sheet_name in excel_file.sheet_names:
        print(f"\n{'='*60}")
        print(f"ANALYZING SHEET: {sheet_name}")
        print(f"{'='*60}")
        
        # Read the sheet
        df = pd.read_excel('combined.xlsx', sheet_name=sheet_name)
        
        print(f"Shape: {df.shape}")
        print(f"Columns: {df.columns.tolist()}")
        print(f"Data types:\n{df.dtypes}")
        
        print(f"\nFirst 5 rows:")
        print(df.head())
        
        print(f"\nBasic statistics:")
        print(df.describe(include='all'))
        
        # Check for missing values
        missing = df.isnull().sum()
        if missing.sum() > 0:
            print(f"\nMissing values:")
            print(missing[missing > 0])
        else:
            print("\nNo missing values found!")
        
        # Show unique values for each column
        print(f"\nUnique values per column:")
        for col in df.columns:
            unique_count = df[col].nunique()
            print(f"  {col}: {unique_count} unique values")
            if unique_count <= 10:
                print(f"    All values: {df[col].unique()}")
            elif unique_count <= 20:
                print(f"    Sample values: {df[col].unique()[:10]}")
        
        # Check for duplicates
        duplicates = df.duplicated().sum()
        print(f"\nDuplicate rows: {duplicates}")
        
        if duplicates > 0:
            print("Sample duplicate rows:")
            print(df[df.duplicated()].head())

except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()
