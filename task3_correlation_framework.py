#!/usr/bin/env python3
"""
Task 3: News Sentiment & Stock Movement Correlation Analysis
Nova Financial Solutions - Predictive Analytics Enhancement
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import seaborn as sns
from scipy.stats import pearsonr, spearmanr
from textblob import TextBlob
from vaderSentiment.vaderSentiment import SentimentIntensityAnalyzer
import warnings
warnings.filterwarnings('ignore')

class NewsStockCorrelationAnalyzer:
    """Analyze correlation between news sentiment and stock price movements"""
    
    def __init__(self, stock_data_path, news_data_path):
        """Initialize with data paths"""
        self.stock_data_path = stock_data_path
        self.news_data_path = news_data_path
        self.stock_data = {}
        self.news_data = None
        self.aligned_data = {}
        self.correlation_results = {}
        
        # Initialize sentiment analyzers
        self.vader_analyzer = SentimentIntensityAnalyzer()
    
    def load_news_data(self):
        """Load and preprocess financial news data"""
        print("📰 Loading financial news data...")
        
        try:
            # Load news data (assuming CSV format)
            self.news_data = pd.read_csv(self.news_data_path)
            
            # Convert date column to datetime
            self.news_data['date'] = pd.to_datetime(self.news_data['date'])
            
            # Extract date only (remove time component for daily aggregation)
            self.news_data['date_only'] = self.news_data['date'].dt.date
            
            print(f"✅ Loaded {len(self.news_data)} news articles")
            print(f"📅 Date range: {self.news_data['date'].min()} to {self.news_data['date'].max()}")
            print(f"📊 Unique stocks: {self.news_data['stock'].nunique()}")
            
            return self.news_data
            
        except Exception as e:
            print(f"❌ Error loading news data: {str(e)}")
            return None
    
    def load_stock_data(self, symbols):
        """Load stock data for specified symbols"""
        print("📈 Loading stock data...")
        
        for symbol in symbols:
            try:
                file_path = f"{self.stock_data_path}/{symbol}_historical_data.csv"
                df = pd.read_csv(file_path)
                df['Date'] = pd.to_datetime(df['Date'])
                df['date_only'] = df['Date'].dt.date
                df.set_index('Date', inplace=True)
                df.sort_index(inplace=True)
                
                # Calculate daily returns
                df['Daily_Return'] = df['Close'].pct_change()
                df['Price_Change'] = df['Close'].diff()
                df['Price_Change_Pct'] = (df['Close'] / df['Close'].shift(1) - 1) * 100
                
                # Calculate volatility
                df['Volatility'] = df['Daily_Return'].rolling(window=20).std()
                
                self.stock_data[symbol] = df
                print(f"✅ Loaded {symbol}: {len(df)} records")
                
            except Exception as e:
                print(f"❌ Error loading {symbol}: {str(e)}")
    
    def analyze_sentiment(self, text):
        """Analyze sentiment using multiple methods"""
        if pd.isna(text) or text == '':
            return {
                'textblob_polarity': 0,
                'textblob_subjectivity': 0,
                'vader_compound': 0,
                'vader_positive': 0,
                'vader_negative': 0,
                'vader_neutral': 0
            }
        
        # TextBlob sentiment
        blob = TextBlob(str(text))
        textblob_polarity = blob.sentiment.polarity
        textblob_subjectivity = blob.sentiment.subjectivity
        
        # VADER sentiment
        vader_scores = self.vader_analyzer.polarity_scores(str(text))
        
        return {
            'textblob_polarity': textblob_polarity,
            'textblob_subjectivity': textblob_subjectivity,
            'vader_compound': vader_scores['compound'],
            'vader_positive': vader_scores['pos'],
            'vader_negative': vader_scores['neg'],
            'vader_neutral': vader_scores['neu']
        }
    
    def process_news_sentiment(self):
        """Process sentiment analysis for all news articles"""
        if self.news_data is None:
            print("❌ No news data loaded")
            return None
            
        print("🔍 Analyzing sentiment for news headlines...")
        
        # Apply sentiment analysis
        sentiment_results = self.news_data['headline'].apply(self.analyze_sentiment)
        sentiment_df = pd.DataFrame(sentiment_results.tolist())
        
        # Combine with original data
        self.news_data = pd.concat([self.news_data, sentiment_df], axis=1)
        
        # Create composite sentiment score
        self.news_data['composite_sentiment'] = (
            self.news_data['textblob_polarity'] + self.news_data['vader_compound']
        ) / 2
        
        # Categorize sentiment
        self.news_data['sentiment_category'] = pd.cut(
            self.news_data['composite_sentiment'],
            bins=[-1, -0.1, 0.1, 1],
            labels=['Negative', 'Neutral', 'Positive']
        )
        
        print("✅ Sentiment analysis completed")
        return self.news_data
    
    def align_news_stock_data(self, symbol):
        """Align news sentiment with stock price movements by date"""
        if symbol not in self.stock_data:
            print(f"❌ No stock data for {symbol}")
            return None
            
        if self.news_data is None:
            print("❌ No news data processed")
            return None
        
        print(f"🔗 Aligning news and stock data for {symbol}...")
        
        # Filter news for specific stock
        stock_news = self.news_data[self.news_data['stock'] == symbol].copy()
        
        if len(stock_news) == 0:
            print(f"❌ No news found for {symbol}")
            return None
        
        # Aggregate daily sentiment scores
        daily_sentiment = stock_news.groupby('date_only').agg({
            'composite_sentiment': ['mean', 'std', 'count'],
            'textblob_polarity': 'mean',
            'vader_compound': 'mean',
            'sentiment_category': lambda x: x.mode().iloc[0] if len(x.mode()) > 0 else 'Neutral'
        }).reset_index()
        
        # Flatten column names
        daily_sentiment.columns = [
            'date_only', 'avg_sentiment', 'sentiment_std', 'news_count',
            'avg_textblob', 'avg_vader', 'dominant_sentiment'
        ]
        
        # Get stock data
        stock_df = self.stock_data[symbol].copy()
        stock_df = stock_df.reset_index()
        stock_df['date_only'] = stock_df['Date'].dt.date
        
        # Merge news sentiment with stock data
        aligned_df = pd.merge(
            stock_df, daily_sentiment,
            on='date_only',
            how='left'
        )
        
        # Fill missing sentiment values
        aligned_df['avg_sentiment'].fillna(0, inplace=True)
        aligned_df['news_count'].fillna(0, inplace=True)
        
        # Calculate next-day returns (for predictive analysis)
        aligned_df['next_day_return'] = aligned_df['Daily_Return'].shift(-1)
        aligned_df['next_day_price_change'] = aligned_df['Price_Change'].shift(-1)
        
        # Calculate sentiment impact windows
        aligned_df['sentiment_lag1'] = aligned_df['avg_sentiment'].shift(1)
        aligned_df['sentiment_lag2'] = aligned_df['avg_sentiment'].shift(2)
        
        self.aligned_data[symbol] = aligned_df
        
        print(f"✅ Aligned data created for {symbol}")
        print(f"📊 Total aligned records: {len(aligned_df)}")
        print(f"📰 Days with news: {(aligned_df['news_count'] > 0).sum()}")
        
        return aligned_df
    
    def calculate_correlations(self, symbol):
        """Calculate correlation between sentiment and stock movements"""
        if symbol not in self.aligned_data:
            print(f"❌ No aligned data for {symbol}")
            return None
        
        df = self.aligned_data[symbol].copy()
        
        # Filter out rows with missing data
        analysis_df = df.dropna(subset=['avg_sentiment', 'Daily_Return'])
        
        if len(analysis_df) == 0:
            print(f"❌ No valid data for correlation analysis for {symbol}")
            return None
        
        print(f"📊 Calculating correlations for {symbol}...")
        
        correlations = {}
        
        # Same-day correlations
        correlations['same_day'] = {
            'sentiment_vs_return': pearsonr(analysis_df['avg_sentiment'], analysis_df['Daily_Return']),
            'sentiment_vs_price_change': pearsonr(analysis_df['avg_sentiment'], analysis_df['Price_Change_Pct']),
            'sentiment_vs_volume': pearsonr(analysis_df['avg_sentiment'], analysis_df['Volume']) if 'Volume' in analysis_df.columns else (0, 1)
        }
        
        # Next-day correlations (predictive)
        next_day_df = analysis_df.dropna(subset=['next_day_return'])
        if len(next_day_df) > 0:
            correlations['next_day'] = {
                'sentiment_vs_next_return': pearsonr(next_day_df['avg_sentiment'], next_day_df['next_day_return']),
                'sentiment_vs_next_price_change': pearsonr(next_day_df['avg_sentiment'], next_day_df['next_day_price_change'])
            }
        
        # Lagged correlations
        lag1_df = analysis_df.dropna(subset=['sentiment_lag1'])
        if len(lag1_df) > 0:
            correlations['lag1'] = {
                'lag1_sentiment_vs_return': pearsonr(lag1_df['sentiment_lag1'], lag1_df['Daily_Return'])
            }
        
        # News volume correlations
        correlations['news_volume'] = {
            'news_count_vs_return': pearsonr(analysis_df['news_count'], analysis_df['Daily_Return']),
            'news_count_vs_volatility': pearsonr(analysis_df['news_count'], analysis_df['Volatility']) if 'Volatility' in analysis_df.columns else (0, 1)
        }
        
        self.correlation_results[symbol] = correlations
        
        # Print results
        print(f"\n📈 Correlation Results for {symbol}:")
        print("=" * 50)
        
        for period, corr_dict in correlations.items():
            print(f"\n{period.upper()} Correlations:")
            for corr_name, (corr_coef, p_value) in corr_dict.items():
                significance = "***" if p_value < 0.001 else "**" if p_value < 0.01 else "*" if p_value < 0.05 else ""
                print(f"  {corr_name}: {corr_coef:.4f} (p={p_value:.4f}) {significance}")
        
        return correlations
    
    def create_correlation_visualization(self, symbol):
        """Create visualization of sentiment-stock correlations"""
        if symbol not in self.aligned_data:
            print(f"❌ No aligned data for {symbol}")
            return None
        
        df = self.aligned_data[symbol].copy()
        df = df.dropna(subset=['avg_sentiment', 'Daily_Return'])
        
        if len(df) == 0:
            print(f"❌ No valid data for visualization for {symbol}")
            return None
        
        # Create subplot figure
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle(f'{symbol} - News Sentiment vs Stock Movement Analysis', fontsize=16)
        
        # 1. Sentiment vs Daily Return Scatter
        axes[0, 0].scatter(df['avg_sentiment'], df['Daily_Return'], alpha=0.6, color='blue')
        axes[0, 0].set_xlabel('Average Daily Sentiment')
        axes[0, 0].set_ylabel('Daily Return (%)')
        axes[0, 0].set_title('Sentiment vs Daily Return')
        
        # Add trend line
        z = np.polyfit(df['avg_sentiment'], df['Daily_Return'], 1)
        p = np.poly1d(z)
        axes[0, 0].plot(df['avg_sentiment'], p(df['avg_sentiment']), "r--", alpha=0.8)
        
        # 2. Time series of sentiment and returns
        df_plot = df.set_index('Date')
        ax2 = axes[0, 1]
        ax2_twin = ax2.twinx()
        
        ax2.plot(df_plot.index, df_plot['avg_sentiment'], color='green', label='Sentiment', alpha=0.7)
        ax2_twin.plot(df_plot.index, df_plot['Daily_Return'], color='red', label='Daily Return', alpha=0.7)
        
        ax2.set_ylabel('Sentiment Score', color='green')
        ax2_twin.set_ylabel('Daily Return', color='red')
        ax2.set_title('Sentiment and Returns Over Time')
        ax2.tick_params(axis='x', rotation=45)
        
        # 3. Sentiment distribution by return direction
        df['return_direction'] = np.where(df['Daily_Return'] > 0, 'Positive', 'Negative')
        
        positive_sentiment = df[df['return_direction'] == 'Positive']['avg_sentiment']
        negative_sentiment = df[df['return_direction'] == 'Negative']['avg_sentiment']
        
        axes[1, 0].hist(positive_sentiment, alpha=0.7, label='Positive Returns', color='green', bins=20)
        axes[1, 0].hist(negative_sentiment, alpha=0.7, label='Negative Returns', color='red', bins=20)
        axes[1, 0].set_xlabel('Sentiment Score')
        axes[1, 0].set_ylabel('Frequency')
        axes[1, 0].set_title('Sentiment Distribution by Return Direction')
        axes[1, 0].legend()
        
        # 4. News volume vs volatility
        if 'Volatility' in df.columns:
            axes[1, 1].scatter(df['news_count'], df['Volatility'], alpha=0.6, color='purple')
            axes[1, 1].set_xlabel('Daily News Count')
            axes[1, 1].set_ylabel('Volatility')
            axes[1, 1].set_title('News Volume vs Stock Volatility')
        else:
            axes[1, 1].text(0.5, 0.5, 'Volatility data\nnot available', 
                           ha='center', va='center', transform=axes[1, 1].transAxes)
            axes[1, 1].set_title('News Volume vs Stock Volatility')
        
        plt.tight_layout()
        return fig
    
    def generate_correlation_report(self, symbols):
        """Generate comprehensive correlation analysis report"""
        print("\n📊 GENERATING CORRELATION ANALYSIS REPORT")
        print("=" * 60)
        
        report_data = []
        
        for symbol in symbols:
            if symbol in self.correlation_results:
                corr = self.correlation_results[symbol]
                
                # Extract key metrics
                same_day_corr = corr['same_day']['sentiment_vs_return'][0]
                same_day_p = corr['same_day']['sentiment_vs_return'][1]
                
                next_day_corr = corr.get('next_day', {}).get('sentiment_vs_next_return', (0, 1))[0]
                next_day_p = corr.get('next_day', {}).get('sentiment_vs_next_return', (0, 1))[1]
                
                news_volume_corr = corr['news_volume']['news_count_vs_return'][0]
                news_volume_p = corr['news_volume']['news_count_vs_return'][1]
                
                report_data.append({
                    'Symbol': symbol,
                    'Same_Day_Correlation': same_day_corr,
                    'Same_Day_P_Value': same_day_p,
                    'Next_Day_Correlation': next_day_corr,
                    'Next_Day_P_Value': next_day_p,
                    'News_Volume_Correlation': news_volume_corr,
                    'News_Volume_P_Value': news_volume_p,
                    'Same_Day_Significant': same_day_p < 0.05,
                    'Next_Day_Significant': next_day_p < 0.05,
                    'Predictive_Potential': abs(next_day_corr) > 0.1 and next_day_p < 0.05
                })
        
        report_df = pd.DataFrame(report_data)
        
        print("\n📈 CORRELATION SUMMARY:")
        print(report_df.to_string(index=False, float_format='%.4f'))
        
        # Summary statistics
        print(f"\n🎯 KEY INSIGHTS:")
        print(f"• Average same-day correlation: {report_df['Same_Day_Correlation'].mean():.4f}")
        print(f"• Average next-day correlation: {report_df['Next_Day_Correlation'].mean():.4f}")
        print(f"• Stocks with significant same-day correlation: {report_df['Same_Day_Significant'].sum()}")
        print(f"• Stocks with predictive potential: {report_df['Predictive_Potential'].sum()}")
        
        return report_df

# Example usage
if __name__ == "__main__":
    # Initialize analyzer
    analyzer = NewsStockCorrelationAnalyzer(
        stock_data_path='./yfinance_extracted/yfinance_data',
        news_data_path='./financial_news.csv'  # Your news data file
    )
    
    # Load and process data
    # analyzer.load_news_data()
    # analyzer.load_stock_data(['AAPL', 'TSLA', 'MSFT'])
    # analyzer.process_news_sentiment()
    
    # Analyze correlations for each stock
    # for symbol in ['AAPL', 'TSLA', 'MSFT']:
    #     analyzer.align_news_stock_data(symbol)
    #     analyzer.calculate_correlations(symbol)
    #     analyzer.create_correlation_visualization(symbol)
    
    # Generate final report
    # report = analyzer.generate_correlation_report(['AAPL', 'TSLA', 'MSFT'])
    
    print("🚀 Correlation analysis framework ready!")
    print("📝 Update the news_data_path with your actual news dataset")
    print("🔧 Run the analysis once you have the news data loaded")
