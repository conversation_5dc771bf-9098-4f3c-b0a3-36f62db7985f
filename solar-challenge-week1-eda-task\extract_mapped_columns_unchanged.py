import pandas as pd

print("🔍 Extracting Unchanged Values from Mapped Columns Only...")

# Step 1: Read the matrices
print("📖 Reading matrices...")
original_matrix = pd.read_excel('combined.xlsx', sheet_name='categorycodeandrolecode')
transformed_matrix = pd.read_excel('final_centered_format.xlsx', sheet_name='4_matrix_readable')

print(f"Original matrix shape: {original_matrix.shape}")
print(f"Transformed matrix shape: {transformed_matrix.shape}")

# Step 2: Define the specific columns you want to analyze
target_columns = [
    'Network Admin',
    'Distributor Shop Manager', 
    'Customer Care',
    'Distributor',
    'Sub Super Admin',
    'Agent',
    'Super Admin',
    'POS',
    'Safaricom Shop',
    'Distributor Sales Person',
    'Channel Admin',
    'Sub Distributor'
]

print(f"Target columns to analyze: {len(target_columns)}")
for col in target_columns:
    print(f"  - {col}")

# Step 3: Find these columns in the transformed matrix and get their original counterparts
print("🔍 Mapping columns between original and transformed matrices...")
column_mapping = {}

for trans_col in transformed_matrix.columns:
    if trans_col in target_columns:
        # Find the corresponding original column index
        trans_col_idx = list(transformed_matrix.columns).index(trans_col)
        if trans_col_idx < len(original_matrix.columns):
            orig_col = original_matrix.columns[trans_col_idx]
            column_mapping[trans_col] = {
                'transformed_name': trans_col,
                'original_name': str(orig_col),
                'column_index': trans_col_idx
            }

print(f"✓ Found {len(column_mapping)} target columns in the data:")
for trans_name, info in column_mapping.items():
    print(f"  {info['original_name']} → {trans_name}")

# Step 4: Compare cell by cell for unchanged values
print("🔍 Comparing matrices cell by cell for unchanged values...")
all_unchanged_data = []

for trans_col, info in column_mapping.items():
    orig_col = info['original_name']
    col_idx = info['column_index']
    
    print(f"Analyzing: {orig_col} → {trans_col}")
    
    # Compare each cell in this column
    unchanged_values = []
    
    for row_idx in range(min(len(original_matrix), len(transformed_matrix))):
        orig_val = original_matrix.iloc[row_idx, col_idx]
        trans_val = transformed_matrix.iloc[row_idx, col_idx]
        
        # Check if both values exist and are exactly the same (unchanged)
        if (pd.notna(orig_val) and pd.notna(trans_val) and 
            str(orig_val).strip() == str(trans_val).strip() and
            str(orig_val).strip() != ''):
            unchanged_values.append(str(orig_val).strip())
    
    # Remove duplicates and add to results
    unique_unchanged = list(set(unchanged_values))
    
    for value in unique_unchanged:
        all_unchanged_data.append({
            'Original_Column': orig_col,
            'Readable_Column': trans_col,
            'Unchanged_Value': value,
            'Status': 'Not transformed - likely missing from role lookup'
        })
    
    print(f"  Found {len(unique_unchanged)} unique unchanged values")

# Step 5: Create final DataFrame
print("📊 Creating final results...")
results_df = pd.DataFrame(all_unchanged_data)

# Sort by column name and then by value
if not results_df.empty:
    results_df = results_df.sort_values(['Readable_Column', 'Unchanged_Value']).reset_index(drop=True)

# Step 6: Create summary statistics
print("📈 Creating summary...")
if not results_df.empty:
    summary_stats = results_df.groupby('Readable_Column').agg({
        'Unchanged_Value': 'count'
    }).rename(columns={'Unchanged_Value': 'Count_Unchanged'}).reset_index()
    summary_stats = summary_stats.sort_values('Count_Unchanged', ascending=False)
else:
    summary_stats = pd.DataFrame()

# Step 7: Save to Excel (single sheet as requested)
print("💾 Saving to single Excel sheet...")
with pd.ExcelWriter('mapped_columns_unchanged_values.xlsx', engine='openpyxl') as writer:
    # Main results - all in one sheet as requested
    results_df.to_excel(writer, sheet_name='UNCHANGED_VALUES_ALL', index=False)
    
    # Summary statistics
    summary_stats.to_excel(writer, sheet_name='SUMMARY', index=False)

print("🎉 SUCCESS! Created 'mapped_columns_unchanged_values.xlsx'")

# Step 8: Show preview
print("\n📊 UNCHANGED VALUES ANALYSIS:")
print(f"Total unchanged values found: {len(results_df)}")
print(f"Columns analyzed: {len(column_mapping)}")

if not summary_stats.empty:
    print("\n📈 SUMMARY BY COLUMN:")
    print(summary_stats.to_string(index=False))

if not results_df.empty:
    print(f"\n📋 SAMPLE UNCHANGED VALUES (first 20):")
    print(results_df.head(20).to_string(index=False))

print(f"\n📁 OUTPUT FILE: mapped_columns_unchanged_values.xlsx")
print("• Sheet 1: UNCHANGED_VALUES_ALL - All unchanged values in one sheet")
print("• Sheet 2: SUMMARY - Count by column")

print(f"\n🎯 INTERPRETATION:")
print("These are role codes that exist in your readable columns but")
print("weren't transformed, indicating they're missing from the role lookup table.")
