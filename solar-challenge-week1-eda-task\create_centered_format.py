import pandas as pd

print("🔄 Creating Centered Category-Role Format...")

# Step 1: Read all sheets
print("📖 Reading Excel sheets...")
roles_df = pd.read_excel('combined.xlsx', sheet_name='role_codeandrolecdescription')
categories_df = pd.read_excel('combined.xlsx', sheet_name='categorycodeandcatagorydescript') 
matrix_df = pd.read_excel('combined.xlsx', sheet_name='categorycodeandrolecode')

# Step 2: Create lookup dictionaries
print("🔍 Creating lookup dictionaries...")
role_dict = dict(zip(roles_df['Role_ Code'], roles_df['Role_Name']))
category_dict = dict(zip(categories_df['Category_code'], categories_df['Description']))

# Step 3: Create centered format
print("📋 Creating centered category-role format...")
centered_data = []

for col_idx, col in enumerate(matrix_df.columns):
    col_str = str(col)
    
    # Get category description
    if col_str in category_dict:
        category_desc = category_dict[col_str]
    else:
        category_desc = col_str  # Keep original if no match
    
    print(f"Processing {col_idx + 1}/{len(matrix_df.columns)}: {category_desc}")
    
    # Get all unique roles in this category
    roles_in_category = matrix_df[col].dropna().unique()
    
    if len(roles_in_category) > 0:
        # Sort roles
        sorted_roles = sorted(roles_in_category, key=str)
        role_descriptions = []
        
        # Get all role descriptions first
        for role_code in sorted_roles:
            role_str = str(role_code)
            if role_str in role_dict:
                role_desc = role_dict[role_str]
            else:
                role_desc = role_str
            role_descriptions.append(role_desc)
        
        # Calculate middle position
        total_roles = len(role_descriptions)
        middle_pos = total_roles // 2
        
        # Add rows with category name in the middle
        for role_idx, role_desc in enumerate(role_descriptions):
            if role_idx == middle_pos:
                # Middle role gets the category name
                centered_data.append({
                    'Category': category_desc,
                    'Role': role_desc
                })
            else:
                # All other roles get empty category
                centered_data.append({
                    'Category': '',
                    'Role': role_desc
                })
        
        # Add separator line after each category group
        centered_data.append({
            'Category': '------------------------------',
            'Role': '------------------------------'
        })

# Remove the last separator
if centered_data and centered_data[-1]['Category'] == '------------------------------':
    centered_data.pop()

# Step 4: Create DataFrame
centered_df = pd.DataFrame(centered_data)

print(f"✓ Created centered format with {len(centered_df)} rows")

# Step 5: Create the other sheets as before
print("🔄 Creating other sheets...")

# Flattened format
flattened_data = []
for col in matrix_df.columns:
    col_str = str(col)
    category_desc = category_dict.get(col_str, col_str)
    
    roles_in_category = matrix_df[col].dropna().unique()
    for role_code in roles_in_category:
        role_str = str(role_code)
        role_desc = role_dict.get(role_str, role_str)
        flattened_data.append({
            'Category': category_desc,
            'Role': role_desc
        })

flattened_df = pd.DataFrame(flattened_data).drop_duplicates().sort_values(['Category', 'Role']).reset_index(drop=True)

# Transformed matrix
transformed_df = matrix_df.copy()
new_columns = [category_dict.get(str(col), str(col)) for col in matrix_df.columns]
transformed_df.columns = new_columns

for col in transformed_df.columns:
    for idx in transformed_df.index:
        cell_value = transformed_df.at[idx, col]
        if pd.notna(cell_value):
            cell_str = str(cell_value)
            if cell_str in role_dict:
                transformed_df.at[idx, col] = role_dict[cell_str]

# Step 6: Save to Excel
print("💾 Saving to Excel file...")
with pd.ExcelWriter('final_centered_format.xlsx', engine='openpyxl') as writer:
    # Original sheets
    roles_df.to_excel(writer, sheet_name='1_roles_original', index=False)
    categories_df.to_excel(writer, sheet_name='2_categories_original', index=False)
    matrix_df.to_excel(writer, sheet_name='3_matrix_original', index=False)
    
    # Transformed matrix
    transformed_df.to_excel(writer, sheet_name='4_matrix_readable', index=False)
    
    # Flattened list
    flattened_df.to_excel(writer, sheet_name='5_flattened_list', index=False)
    
    # NEW: Centered format - category name in middle of role group
    centered_df.to_excel(writer, sheet_name='6_CENTERED_FORMAT', index=False)

print("🎉 SUCCESS! Created 'final_centered_format.xlsx'")

# Step 7: Show preview
print("\n📊 CENTERED FORMAT PREVIEW:")
print(f"Total rows: {len(centered_df)}")
print("\nFirst 20 rows showing the centered format:")

for i, row in centered_df.head(20).iterrows():
    category_part = row['Category'] if row['Category'] else ''
    role_part = row['Role']
    print(f"{category_part:<25} | {role_part}")

print(f"\n📁 FILE STRUCTURE:")
print("• Sheet 1: Original roles")
print("• Sheet 2: Original categories")  
print("• Sheet 3: Original matrix")
print("• Sheet 4: Readable matrix")
print("• Sheet 5: Flattened list (Category | Role)")
print("• Sheet 6: CENTERED FORMAT (Category name in middle of role group)")

print(f"\n✅ File ready: final_centered_format.xlsx")
print("📝 This format shows category names in the middle of their role groups, exactly as requested!")
